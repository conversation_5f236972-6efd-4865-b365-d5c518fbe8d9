let db = "";
const { ObjectId } = require("mongodb");
const common = require("../../common");
const dayjs = require("dayjs");
const { formatUploadRxData } = require("./format");
const zytHis = require("../zyt-his");
const consultOrder = require("../consult-order");
const tencentIM = require("../tencent-im");
const beijingCa = require("../beijing-ca");
const BigNumber = require('bignumber.js');
const timeApi = require('../hlw-time-duration');

/**
 * 性能优化建议：
 * 
 * 1. 数据库索引建议：
 *    - diagnostic-record 集合：
 *      db.getCollection("diagnostic-record").createIndex({ "status": 1, "createTime": -1 })
 *      db.getCollection("diagnostic-record").createIndex({ "orderId": 1 })
 *      db.getCollection("diagnostic-record").createIndex({ "doctorCode": 1, "status": 1 })
 *      db.getCollection("diagnostic-record").createIndex({ "patientId": 1, "status": 1, "orderSource": 1 })
 * 
 *    - consult-order 集合：
 *      db.getCollection("consult-order").createIndex({ "orderId": 1 })
 *      db.getCollection("consult-order").createIndex({ "orderStatus": 1 })
 * 
 * 2. 查询优化：
 *    - getAuditDiagnosisList 函数已优化为先分页后关联查询，避免大数据量 $lookup
 *    - 使用 Promise.all 并行执行不相关的查询操作
 *    - 避免在查询条件中使用正则表达式，考虑使用全文检索
 * 
 * 3. 数据冗余建议（可选）：
 *    - 在 diagnostic-record 中冗余 consult-order 的关键字段（如患者姓名、手机号等）
 *    - 减少跨集合查询的需求
 */

// 判断当前是否是 zytDev 环境
function isZytDevEnv() {
  console.log("当前环境变量 CONFIG_NODE_ENV:", process.env.CONFIG_NODE_ENV);
  return process.env.CONFIG_NODE_ENV === "zytDev" || process.env.CONFIG_NODE_ENV === "development";
}

// 根据环境决定是否调用 beijingCa
async function conditionalCallBeijingCa(params, dbParam) {
  if (isZytDevEnv()) {
    console.log("在 zytDev 环境下，跳过调用 beijingCa");
    return {
      success: true,
      userId: params.code || params.userId || "mock_user_id",
      message: "在 zytDev 环境下模拟返回",
    };
  }
  return await beijingCa(params, dbParam);
}

const status = {
  init: "INIT", //待审核
  pass: "PASS", //审核通过
  unpass: "UNPASS", //审核未通过
  expired: "EXPIRED", //订单过期 无法
  passInvalid: "PASS_INVALID", //通过的处方作废 (目前就订单退费会导致处方作废)
  unpassInvalid: "UNPASS_INVALID", //未通过的处方作废 (目前就订单退费会导致处方作废)
  invalid: 'INVALID', //处方作废 (目前就订单退费会导致处方作废)
};

module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addConsultDiagnosis":
      return await addConsultDiagnosis(item, db);
    case "getOrderDiagnosis":
      return await getOrderDiagnosis(item, db);
    case "getStoreDiagnosisRecord":
      return await getStoreDiagnosisRecord(item, db);
    case "getAuditDiagnosisList":
      return await getAuditDiagnosisList(item, db);
    case "getAuditedDiagnosisList":
      return await getAuditedDiagnosisList(item, db);
    case "getPatientDiagnosisRecord":
      return await getPatientDiagnosisRecord(item, db);
    case "getPatientPassDiagnosisRecord":
      return await getPatientPassDiagnosisRecord(item, db);
    case "auditDiagnosis":
      return await auditDiagnosis(item, db);
    case "uploadDiagnosis":
      return await uploadDiagnosis(item, db);
    case "orderHasDiagnosis":
      return await orderHasDiagnosis(item, db);
    case "getLatestSubmitTime":
      return await getLatestSubmitTime(item, db);
    case "reUploadDiagnosticRecord":
      return await reUploadDiagnosticRecord(item, db);
    case "discardDiagnosticRecord":
      return await discardDiagnosticRecord(item, db);
    case "getAllDiagnosisRecord":
      return await getAllDiagnosisRecord(item, db);
    case "getDoctorRxStats":
      return await getDoctorRxStats(item, db);
    case "getPharmacistRxStats":
      return await getPharmacistRxStats(item, db);
    case "checkRxUploadStatus":
      return await checkRxUploadStatus(item, db);
  }
};

// 咨询订单库 数据库是 diagnostic-record
async function addConsultDiagnosis(item) {
  let { params = {}, corpId } = item;
  const { orderId, doctorCode } = params;
  if (
    typeof orderId !== "string" ||
    orderId.trim() === "" ||
    typeof doctorCode !== "string" ||
    doctorCode.trim() === ""
  ) {
    return { success: false, message: "参数错误" };
  }
  try {
    const order = await db.collection('consult-order').findOne({ orderId }, { projection: { createTime: 1, orderStatus: 1, registerId: 1, medorg_order_no: 1 } });
    if (!order) {
      return { success: false, message: "订单不存在" };
    }
    if (order.orderStatus !== "processing") {
      return { success: false, message: "当前订单状态不支持开方" };
    }
    if (!(order.createTime > dayjs().startOf('day').valueOf())) {
      return { success: false, message: "当前订单不在有效期内" };
    }
    timeApi({ type: "addDoctorRxDuration", orderId }, db);
    // 提交诊断前, 先判单CA 自动签名是否失效
    const res = await conditionalCallBeijingCa({
      type: "autoSignVerifyCA",
      code: doctorCode,
    });
    const doctorCAUserId = res.userId;
    if (!res.success) {
      return { success: false, message: "CA自动签名已失效, 请开启自动签" };
    }
    // 判断是否医生发的消息是否大于2条
    const chatCount = await db.collection("im-chat-msg").countDocuments({
      From_Account: doctorCode,
      To_Account: orderId,
    });
    if (chatCount < 3) {
      return {
        success: false,
        message: "请先向患者发送2条提问消息后再开方",
      };
    }
    const record = await db
      .collection("diagnostic-record")
      .findOne({ orderId, doctorCode }, { projection: { _id: 1, status: 1, pharmacistNo: 1 } });
    if (record && record.status === status.init) {
      return { success: false, message: "该订单已存在诊断信息，请勿重复添加" };
    } else if (record && record.status === status.pass) {
      return { success: false, message: "该订单已审核通过，请勿重复添加" };
    } else if (record && record.status === status.unpass) {
      return await rewriteConsultDiagnosis(params, record._id, corpId, record.pharmacistNo);
    }
    // 获取推荐药师 随机获取药师
    const { success, message, data: pharmacist } = await getRecommendPharmacist();
    if (!success) {
      return { success: false, message }
    }
    const data = {
      ...params,
      createTime: Date.now(),
      status: status.init,
      pharmacistNo: pharmacist.pharmacistNo,
      pharmacist: pharmacist.pharmacistName,
      doctorCAUserId,
    };
    if (!data.medOrgOrderNo) {
      data.medOrgOrderNo = order.registerId || order.medorg_order_no;
    }
    const { insertedId } = await db
      .collection("diagnostic-record")
      .insertOne(data);
    // 发起咨询后, 咨询订单状态变为已完成
    await updateOrderStatusAndSendNotification({
      orderId,
      corpId,
      doctorCode,
      notification: "处方已开具，审方中.....请稍等",
      syncOtherMachine: 1,
      orderStatus: "completed",
      msgType: "MEDICALADVICE",
    });
    return {
      success: true,
      message: "新增成功",
      data: insertedId,
      doctorCAUserId,
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "新增失败",
    };
  }
}
// 未通过的处方 重新提交 原药师在线的话 
async function rewriteConsultDiagnosis(item, _id, corpId, pharmacistNo) {
  const {
    complaint,
    presentIllness,
    dispose,
    diagnosisList,
    drugs,
    orderId,
    doctorCode,
  } = item;
  try {
    const data = {
      complaint,
      presentIllness,
      dispose,
      diagnosisList,
      drugs,
      updateTime: Date.now(),
      status: status.init,
    };
    const { success, message, data: pharmacist } = await getRecommendPharmacist(pharmacistNo);
    if (!success) {
      return { success: false, message }
    }
    // 如果药师不在线, 则重新获取药师 doctorNo doctorName
    if (pharmacist && pharmacist.doctorNo !== pharmacistNo) {
      data.pharmacistNo = pharmacist.doctorNo;
      data.pharmacist = pharmacist.doctorName;
    }
    const { modifiedCount } = await db
      .collection("diagnostic-record")
      .updateOne({ _id }, { $set: data });
    await updateOrderStatusAndSendNotification({
      orderId,
      corpId,
      doctorCode,
      notification: "医生重新提交诊断, 等待审方中",
      syncOtherMachine: 2,
      orderStatus: "completed",
      msgType: "REPEATMEDICALADVICE",
    });
    if (modifiedCount === 1) {
      return { success: true, message: "更新成功" };
    }
    return { success: false, message: "更新失败" };
  } catch (e) {
    return { success: false, message: e.message || "更新失败" };
  }
}

async function getOrderDiagnosis(item, db) {
  const { orderId, doctorNo: doctorCode, status } = item;
  if (typeof orderId !== "string" || orderId.trim() === "") {
    return { success: false, message: "参数错误" };
  }
  try {
    const query = { orderId, doctorCode };
    if (typeof status === "string" && status.trim() !== "") {
      query.status = status;
    }
    const data = await db.collection("diagnostic-record").findOne(query);
    return { success: true, message: "查询成功", data };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getStoreDiagnosisRecord(item, db) {
  if (typeof item.storeId !== "string" || item.storeId.trim() === "") {
    return { success: false, message: "参数错误" };
  }
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 20;
  const date = item.date && dayjs(item.date).isValid() ? dayjs(item.date) : dayjs();
  const createTime = {
    $gte: date.startOf("day").valueOf(),
    $lte: date.endOf("day").valueOf(),
  }
  const query = {
    drugStoreNo: item.storeId,
    orderSource: "MATEPAD",
    status: status.pass,
    createTime,
  };
  if (typeof item.keyword == "string" && item.keyword.trim() !== "") {
    query.$or = [
      { name: { $regex: item.keyword.trim(), $options: "i" } },
      { doctorName: { $regex: item.keyword.trim(), $options: "i" } },
    ]
  }
  try {
    const total = await db
      .collection("diagnostic-record")
      .countDocuments(query);
    const list = await db
      .collection("diagnostic-record")
      .find(query)
      .sort({ auditTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    // 查询今日的处方 对已上传但未获取 his处方上传状态的处方进行查询
    const start = dayjs().startOf("day").valueOf();
    const toSignList = list
      .filter(
        (i) =>
          i.patientId &&
          i.medOrgOrderNo &&
          i.status === status.pass &&
          i.uploadStatus === "uploaded" &&
          !i.hisUploadStatus &&
          i.createTime > start
      )
      .map((i) => ({
        _id: i._id,
        patientId: i.patientId,
        medOrgOrderNo: i.medOrgOrderNo,
      }));
    if (toSignList.length > 0) {
      const task = toSignList.map((data) =>
        zytHis({
          type: "getHisPrescriptionUploadStatus",
          orderno: data.medOrgOrderNo,
          patientId: data.patientId,
          _id: data._id,
        })
      );
      const res = await Promise.all(task);
      const result = res.reduce(
        (acc, item) => {
          if (item.hisUploadStatus === "uploaded") {
            acc.uploaded.push(item._id);
          } else if (item.hisUploadStatus === "error") {
            acc.uploadError.push(item._id);
          }
          if (item.hisUploadStatus) {
            acc.map[item.orderno] = item.hisUploadStatus;
          }
          return acc;
        },
        { uploaded: [], uploadError: [], map: {} }
      );
      if (result.uploaded.length > 0) {
        await db
          .collection("diagnostic-record")
          .updateMany(
            { _id: { $in: result.uploaded } },
            { $set: { hisUploadStatus: "uploaded" } }
          );
      }
      if (result.uploadError.length > 0) {
        await db
          .collection("diagnostic-record")
          .updateMany(
            { _id: { $in: result.uploadError } },
            { $set: { hisUploadStatus: "error" } }
          );
      }
      list.forEach((i) => {
        if (result.map[i.orderId]) {
          i.hisUploadStatus = result.map[i.orderId];
        }
      });
    }
    return {
      success: true,
      message: "查询成功",
      list,
      total,
      paegs: Math.ceil(total / pageSize),
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getAuditDiagnosisList(item, db) {
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 1;
  const query = { status: status.init, pharmacistNo: typeof item.pharmacistNo === "string" ? item.pharmacistNo : "" };
  if (typeof item.name === "string" && item.name.trim() !== "") {
    query.name = new RegExp(item.name);
  }
  if (typeof item.mobile === "string" && item.mobile.trim() !== "") {
    query.mobile = new RegExp(item.mobile);
  }
  if (Array.isArray(item.doctorCodes) && item.doctorCodes.length > 0) {
    query.doctorCode = { $in: item.doctorCodes };
  }
  try {
    // 并行执行总数查询和最新时间查询
    const [total, res] = await Promise.all([
      db.collection("diagnostic-record").countDocuments(query),
      db.collection("diagnostic-record").findOne(
        {},
        {
          sort: { createTime: -1 },
          projection: { createTime: 1, _id: 0 },
        }
      )
    ]);

    // 先查询分页的 diagnostic-record 数据
    const diagnosticRecords = await db
      .collection("diagnostic-record")
      .find(query)
      .sort({ createTime: 1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    if (diagnosticRecords.length === 0) {
      return {
        success: true,
        message: "查询成功",
        list: [],
        total,
        latestTime: res && res.createTime ? res.createTime : 0,
      };
    }

    // 提取所有 orderId
    const orderIds = diagnosticRecords.map(record => record.orderId);

    // 批量查询对应的 consult-order 数据
    const orders = await db
      .collection("consult-order")
      .find({ orderId: { $in: orderIds } })
      .toArray();

    // 创建 orderId 到 order 的映射
    const orderMap = orders.reduce((map, order) => {
      map[order.orderId] = order;
      return map;
    }, {});

    // 合并数据
    const list = diagnosticRecords.map(record => ({
      ...record,
      order: orderMap[record.orderId] || {}
    }));

    return {
      success: true,
      message: "查询成功",
      list,
      total,
      latestTime: res && res.createTime ? res.createTime : 0,
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败", latestTime: 0 };
  }
}

async function getPatientDiagnosisRecord(item, db) {
  if (!item.patientId) {
    return { success: false, message: "参数错误" };
  }
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const orderSource = ['ALIPAY_MINI', 'MATEPAD'].includes(item.orderSource) ? item.orderSource : 'ALIPAY_MINI';
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 20;
  const query = { patientId: item.patientId, status: status.pass, orderSource };
  try {
    const total = await db
      .collection("diagnostic-record")
      .countDocuments(query);
    const list = await db
      .collection("diagnostic-record")
      .find(query, { sort: { auditTime: -1 } })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    // 查询今日的处方 对已上传但未获取 his处方上传状态的处方进行查询
    const start = dayjs().startOf("day").valueOf();
    const toSignList = list
      .filter(
        (i) =>
          i.patientId &&
          i.medOrgOrderNo &&
          i.status === status.pass &&
          i.uploadStatus === "uploaded" &&
          !i.hisUploadStatus &&
          i.createTime > start
      )
      .map((i) => ({
        _id: i._id,
        patientId: i.patientId,
        medOrgOrderNo: i.medOrgOrderNo,
      }));
    if (toSignList.length > 0) {
      const task = toSignList.map((data) =>
        zytHis({
          type: "getHisPrescriptionUploadStatus",
          orderno: data.medOrgOrderNo,
          patientId: data.patientId,
          _id: data._id,
        })
      );
      const res = await Promise.all(task);
      const result = res.reduce(
        (acc, item) => {
          if (item.hisUploadStatus === "uploaded") {
            acc.uploaded.push(item._id);
          } else if (item.hisUploadStatus === "error") {
            acc.uploadError.push(item._id);
          }
          if (item.hisUploadStatus) {
            acc.map[item.orderno] = item.hisUploadStatus;
          }
          return acc;
        },
        { uploaded: [], uploadError: [], map: {} }
      );
      if (result.uploaded.length > 0) {
        await db
          .collection("diagnostic-record")
          .updateMany(
            { _id: { $in: result.uploaded } },
            { $set: { hisUploadStatus: "uploaded" } }
          );
      }
      if (result.uploadError.length > 0) {
        await db
          .collection("diagnostic-record")
          .updateMany(
            { _id: { $in: result.uploadError } },
            { $set: { hisUploadStatus: "error" } }
          );
      }
      list.forEach((i) => {
        if (result.map[i.orderId]) {
          i.hisUploadStatus = result.map[i.orderId];
        }
      });
    }
    return {
      success: true,
      message: "查询成功",
      list,
      total,
      paegs: Math.ceil(total / pageSize),
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}
async function getPatientPassDiagnosisRecord(ctx, db) {
  const { startDate, endDate, patientId } = ctx
  if (typeof patientId !== "string" || patientId.trim() === "") {
    return { success: false, message: "参数错误" };
  }
  const startTime = startDate && dayjs(startDate).isValid() ? dayjs(startDate).startOf('day').valueOf() : '';
  const endTime = endDate && dayjs(endDate).isValid() ? dayjs(endDate).endOf('day').valueOf() : '';
  try {
    const query = { patientId, status: status.pass };
    if (startTime && endTime) {
      query.createTime = { $gte: startTime, $lte: endTime };
    }
    const res = await db.collection("diagnostic-record").find(query).sort({ createTime: -1 }).toArray();
    return { success: true, message: "查询成功", data: res };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getAuditedDiagnosisList(item, db) {
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 1;
  const query = {
    status: { $in: [status.pass, status.unpass, status.expired, status.invalid, status.passInvalid, status.unpassInvalid] },
    pharmacistNo: typeof item.pharmacistNo === "string" ? item.pharmacistNo : "",
  };
  if (typeof item.name === "string" && item.name.trim() !== "") {
    query.name = new RegExp(item.name);
  }
  if (typeof item.mobile === "string" && item.mobile.trim() !== "") {
    query.mobile = new RegExp(item.mobile.trim());
  }
  if (Array.isArray(item.doctorCodes) && item.doctorCodes.length > 0) {
    query.doctorCode = { $in: item.doctorCodes };
  }
  try {
    const total = await db
      .collection("diagnostic-record")
      .countDocuments(query);

    // 先查询分页的 diagnostic-record 数据，按审核时间排序
    const diagnosticRecords = await db
      .collection("diagnostic-record")
      .find(query)
      .sort({ auditTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    if (diagnosticRecords.length === 0) {
      return {
        success: true,
        message: "查询成功",
        list: [],
        total,
      };
    }

    // 提取所有 orderId
    const orderIds = diagnosticRecords.map(record => record.orderId);

    // 批量查询对应的 consult-order 数据
    const orders = await db
      .collection("consult-order")
      .find({ orderId: { $in: orderIds } })
      .toArray();

    // 创建 orderId 到 order 的映射
    const orderMap = orders.reduce((map, order) => {
      map[order.orderId] = order;
      return map;
    }, {});

    // 合并数据
    const list = diagnosticRecords.map(record => ({
      ...record,
      order: orderMap[record.orderId] || {}
    }));

    return {
      success: true,
      message: "查询成功",
      list,
      total,
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}
async function auditDiagnosis(item, db) {
  const { ids, pharmacistNo, pharmacist } = item;
  const res = await conditionalCallBeijingCa({
    type: "autoSignVerifyCA",
    code: pharmacistNo,
  });
  if (!res.success) {
    return { success: false, message: "CA自动签名已失效, 请开启自动签" };
  }
  const pharmacistCAUserId = res.userId;
  if (![status.pass, status.unpass].includes(item.status)) {
    return { success: false, message: "状态错误" };
  }
  if (!pharmacistNo || !pharmacist) {
    return { success: false, message: "审方药师信息不能为空" };
  }
  if (!Array.isArray(ids) || ids.length === 0) {
    return { success: false, message: "id错误" };
  }
  const reasons = Array.isArray(item.reasons)
    ? item.reasons.filter((i) => typeof i === "string" && i.trim() !== "")
    : [];
  if (item.status === status.unpass && reasons.length === 0) {
    return { success: false, message: "请填写拒绝原因" };
  }
  try {
    const _ids = ids.map((id) => new ObjectId(id));
    const recordIds = await db
      .collection("diagnostic-record")
      .find(
        { _id: { $in: _ids }, status: status.init },
        { projection: { orderId: 1, _id: 1 } }
      )
      .toArray();
    // 查询未过期的咨询订单id
    const noExpiredOrderIds = await db
      .collection("consult-order")
      .find(
        {
          orderId: { $in: recordIds.map((i) => i.orderId) },
          orderStatus: { $in: ["pending", "processing", "completed"] },
        },
        { projection: { orderId: 1 } }
      )
      .toArray();
    // 根据查询到的咨询订单id 筛选出需要审核的诊断id
    const toAuditIds = recordIds
      .filter((i) => noExpiredOrderIds.some((j) => j.orderId === i.orderId))
      .map((i) => i._id);
    const expiredRecordIds = recordIds.filter(
      (i) => !toAuditIds.some((j) => j.toString() === i._id.toString())
    );

    const expiredRes = await db.collection("diagnostic-record").updateMany(
      {
        _id: { $in: expiredRecordIds.map((i) => i._id) },
        status: status.init,
      },
      { $set: { status: status.expired } }
    );
    const res = await db.collection("diagnostic-record").updateMany(
      { _id: { $in: toAuditIds }, status: status.init },
      {
        $set: {
          status: item.status,
          auditTime: Date.now(),
          reasons,
          pharmacistNo,
          pharmacist,
        },
      }
    );
    if (item.status === status.unpass) {
      //记录一下 药师拒绝的操作
      db.collection('pharmacist-reject-record').insertMany(toAuditIds.map(id => ({
        recordId: id.toString(),
        type: 'pharmacistReject',
        pharmacistNo,
        pharmacist,
        reasons,
        createTime: Date.now(),
      })))
    }
    const { matchedCount, modifiedCount } = res;
    const {
      matchedCount: expiredMatchedCount,
      modifiedCount: expiredModifiedCount,
    } = expiredRes;
    // 批量处理订单
    let msg = `找到${matchedCount + expiredMatchedCount
      }条数据, 已通过${modifiedCount}条数据`;
    if (expiredModifiedCount > 0) {
      msg += `, 已过期${expiredMatchedCount}条数据`;
    }
    if (toAuditIds.length) {
      batchHandleOrderByAudit(toAuditIds, pharmacistCAUserId, pharmacist);
    }
    if (item.status === status.pass) {
      await uploadDiagnosis(toAuditIds);
      return {
        success: true,
        message: msg,
      };
    }
    return {
      success: true,
      message: `找到${matchedCount}条数据, 已不通过${modifiedCount}条数据`,
    };
  } catch (e) {
    return { success: false, message: e.message || "审核失败" };
  }
}
async function batchHandleOrderByAudit(toAuditIds, pharmacistCAUserId) {
  console.log("批量处理订单", toAuditIds);
  // 利用promise.all处理多个异步任务
  await Promise.all(
    toAuditIds.map(async (_id) => {
      const res = await db.collection("diagnostic-record").findOne({ _id });
      if (!res) {
        console.log("诊断记录不存在");
        return;
      }
      await handleOrderByAudit(res, pharmacistCAUserId);
    })
  );
}

// 根据审核状态, 处理订单
async function handleOrderByAudit(item, pharmacistCAUserId) {
  const { orderId, reasons, doctorCode, pharmacist } = item;
  const order = await db.collection("consult-order").findOne({ orderId });
  if (!order) {
    return {
      success: false,
      message: "订单不存在",
    };
  }
  if (order.orderStatus === "cancelled" || order.orderStatus === "finished") {
    console.log("订单已取消或已完成");
    return;
  }
  if (item.status === status.pass) {
    // 签名
    await sign(item, pharmacistCAUserId);
    await tencentIM({
      type: "sendSystemNotification",
      corpId: order.corpId,
      formAccount: doctorCode,
      toAccount: orderId,
      SyncOtherMachine: 1,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "AUDIPASS",
            Ext: "处方审核通过，请联系店员。",
          },
        },
      ],
    });
  } else if (item.status === status.unpass) {
    // 不通过  发送消息
    // 数组转字符串
    const reason = reasons.join(",");
    await updateOrderStatusAndSendNotification({
      orderId,
      corpId: order.corpId,
      doctorCode,
      notification: `药师${pharmacist}审方不通过，驳回原因：${reason}`,
      syncOtherMachine: 2,
      orderStatus: "processing",
      msgType: "AUDIFAIL",
    });
  }
}
// 对医生和药师进行签名
async function sign(item, pharmacistCAUserId) {
  const { doctorCAUserId, pharmacist } = item;
  console.log("进入签名");
  const oriData = getOriData(item);
  const doctorRes = await conditionalCallBeijingCa(
    {
      type: "autoSign",
      userId: doctorCAUserId,
      oriData,
    },
    db
  );
  const pharmacistRes = await conditionalCallBeijingCa(
    {
      type: "autoSign",
      userId: pharmacistCAUserId,
      oriData: `${oriData} 药师${pharmacist}审方通过`,
    },
    db
  );
  console.log("医生签名结果", doctorRes);
  console.log("药师签名结果", pharmacistRes);
}

function getOriData(data) {
  const doctorName = data.doctorName;
  const patientName = data.name;
  const diagnosis = data.diagnosisList.map((d) => d.name).join(", ");
  const drugs = data.drugs
    .map(
      (d) =>
        `${d.drugName} (${d.dosage}${d.dosage_unit}, ${d.frequencyName}, ${d.days}天)`
    )
    .join(", ");
  return `医生${doctorName}向患者${patientName}开具的诊断是${diagnosis}，开具的药物是${drugs}。`;
}

async function uploadDiagnosis(ids) {
  if (!Array.isArray(ids) || ids.length == 0)
    return {
      success: false,
      message: "参数错误",
    };
  try {
    const res = await db
      .collection("diagnostic-record")
      .find({ _id: { $in: ids }, status: status.pass })
      .limit(ids.length)
      .toArray();
    const list = res.map((item) => ({
      payload: formatUploadRxData(item),
      _id: item._id,
    }));
    const res1 = await Promise.all(
      list.map((item) =>
        zytHis({
          type: "hlwuploadprescription",
          payload: item.payload,
          _id: item._id,
        })
      )
    );
    const successIds = res1.filter((i) => i.success).map((i) => i._id);
    await db
      .collection("diagnostic-record")
      .updateMany(
        { _id: { $in: successIds } },
        { $set: { uploadStatus: "uploaded" } }
      );
    const successCount = successIds.length;
    return {
      success: true,
      message: `成功上传${successCount}条数据`,
    };
  } catch (err) {
    console.log(err, err.message);
    return {
      success: false,
      message: err.message || "上传失败",
    };
  }
}

/**
 *
 * orderStatus  completed(已完成) processing(处理中) pending(待处理) cancelled(已取消) finished(已结束)
 * syncOtherMachine 若不希望将消息同步至 From_Account，则 SyncOtherMachine 填写2；若希望将消息同步至 From_Account，则 SyncOtherMachine 填写1。
 * MsgType TIMTextElem（文本消息）  TIMLocationElem（位置消息） TIMFaceElem（表情消息） TIMCustomElem（自定义消息） TIMSoundElem（语音消息） TIMImageElem（图像消息） TIMFileElem（文件消息） TIMVideoFileElem（视频消息）
 * MsgContent.Text 文本消息内容
 * MsgContent.Data 自定义消息类型 MEDICALADVICE(医嘱)
 * MsgContent.Desc notification
 *
 */
async function updateOrderStatusAndSendNotification(item) {
  const {
    orderId,
    doctorCode,
    corpId,
    notification,
    syncOtherMachine,
    orderStatus,
    msgType,
  } = item;
  await consultOrder(
    {
      type: "updateConsultOrderStatus",
      orderId,
      orderStatus,
      corpId,
    },
    db
  );
  await tencentIM({
    type: "sendSystemNotification",
    corpId,
    formAccount: orderId,
    toAccount: doctorCode,
    SyncOtherMachine: syncOtherMachine,
    msgBody: [
      {
        MsgType: "TIMCustomElem",
        MsgContent: {
          Data: msgType,
          Desc: "notification",
          Ext: notification,
        },
      },
    ],
  });
}

async function getLatestSubmitTime(ctx) {
  try {
    const pharmacistNo = typeof ctx.pharmacistNo === "string" ? ctx.pharmacistNo : "";
    const res = await db.collection("diagnostic-record").findOne(
      { pharmacistNo },
      {
        sort: { createTime: -1 }, // Sort by createTime in descending order
        projection: { createTime: 1, _id: 0 }, // Only return the createTime field, exclude _id
      }
    );
    return {
      success: true,
      message: "获取成功",
      data: res ? res.createTime : 0,
    };
  } catch (e) {
    return { success: false, message: e.message || "获取失败", data: 0 };
  }
}

async function orderHasDiagnosis(item) {
  if (typeof item.patientId !== "string" || typeof item.orderId !== "string") {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const count = await db.collection("diagnostic-record").countDocuments({
      patientId: item.patientId,
      orderId: item.orderId,
    });
    return { success: true, message: "查询成功", exist: count > 0 };
  } catch (e) {
    return { success: true, message: "查询成功", exist: false };
  }
}

async function reUploadDiagnosticRecord({ _id }) {
  try {
    const data = await db
      .collection("diagnostic-record")
      .findOne({ _id: new ObjectId(_id) });
    if (!data) return { success: false, message: "处方不存在" };
    if (data && data.uploadStatus === "uploaded")
      return { success: false, message: "处方已上传" };
    const { uploaded } = await zytHis({
      type: "hlwPrescriptionOrderStatus",
      orderno: data.orderId,
      patientId: data.patientId,
    });
    if (uploaded) {
      await db
        .collection("diagnostic-record")
        .updateOne(
          { _id: new ObjectId(_id) },
          { $set: { uploadStatus: "uploaded", hisUploadStatus: "uploaded" } }
        );
      return { success: true, message: "处方上传成功" };
    }
    const payload = formatUploadRxData(data);
    const { success, message } = await zytHis({
      type: "hlwuploadprescription",
      payload,
      _id: data._id,
    });
    if (success) {
      await db
        .collection("diagnostic-record")
        .updateOne(
          { _id: new ObjectId(_id) },
          { $set: { uploadStatus: "uploaded" } }
        );
      return { success: true, message: "处方上传成功" };
    }
    return { success: false, message: message || "重新上传失败" };
  } catch (e) {
    return { success: false, message: e.message || "重新上传失败" };
  }
}

// 废弃处方
async function discardDiagnosticRecord(ctx) {
  const { orderId } = ctx;
  if (typeof orderId !== 'string' || orderId.trim() === '') return { success: false, message: "参数错误" };
  try {
    const item = await db.collection("diagnostic-record").findOne({ orderId, status: { $nin: [status.passInvalid, status.invalid, status.unpassInvalid] } }, { projection: { status: 1, _id: 1 } });
    if (item) {
      const targetStatus = item.status === status.pass ? status.passInvalid : item.status === status.unpass ? status.unpassInvalid : status.invalid;
      await db.collection("diagnostic-record").updateOne({ _id: item._id }, {
        $set: {
          status: targetStatus
        }
      });
      return { success: true, message: "废弃成功" };
    }
    return { success: false, message: "处方不存在" };
  } catch (e) {
    return { success: false, message: e.message || "废弃失败" };
  }
}

async function getAllDiagnosisRecord(ctx) {
  const { page, pageSize } = ctx;
  const pageNum = page > 0 && Number.isInteger(page) ? page : 1;
  const size = pageSize > 0 && Number.isInteger(pageSize) ? pageSize : 20;
  try {
    const query = {};
    if (typeof ctx.name === 'string' && ctx.name.trim() !== "") {
      query.name = new RegExp(ctx.name);
    }
    if (Array.isArray(ctx.doctorCodes) && ctx.doctorCodes.length > 0) {
      query.doctorCode = { $in: ctx.doctorCodes };
    }
    if (Array.isArray(ctx.pharmacistNos) && ctx.pharmacistNos.length > 0) {
      query.pharmacistNo = { $in: ctx.pharmacistNos };
    }
    const { startDate, endDate } = ctx;
    if (startDate && dayjs(startDate).isValid()) {
      query.createTime = { $gte: dayjs(startDate).startOf('day').valueOf() };
    }
    if (endDate && dayjs(endDate).isValid()) {
      query.createTime = { ...(query.createTime || {}), $lte: dayjs(endDate).endOf('day').valueOf() };
    }
    const { auditStartDate, auditEndDate } = ctx;
    if (auditStartDate && dayjs(auditStartDate).isValid()) {
      query.auditTime = { $gte: dayjs(auditStartDate).startOf('day').valueOf() };
    }
    if (auditEndDate && dayjs(auditEndDate).isValid()) {
      query.auditTime = { ...(query.auditTime || {}), $lte: dayjs(auditEndDate).endOf('day').valueOf() };
    }
    if (Array.isArray(ctx.status)) {
      query.status = { $in: ctx.status };
    }
    const total = await db.collection("diagnostic-record").countDocuments(query);
    const list = await db.collection("diagnostic-record").find(query).sort({ createTime: -1 }).skip((pageNum - 1) * size).limit(size).toArray();
    return { success: true, message: "查询成功", list, total, pages: Math.ceil(total / size) };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getRecommendPharmacist(pharmacistNo = "") {
  try {
    const nos = await db.collection("hlw-doctor").find({ job: 'pharmacist', onlineStatus: 'online', serviceStatus: 'enabled' }, { projection: { doctorNo: 1, doctorName: 1 } }).toArray();
    const pharmacistNos = nos.map(item => item.doctorNo);
    const matchPharmacist = nos.find(item => pharmacistNo && item.doctorNo === pharmacistNo);
    if (matchPharmacist) {
      return { success: true, message: "获取推荐药师成功", data: matchPharmacist }
    }
    if (nos.length === 0) {
      return { success: false, message: "当前没有药师在线，请稍后处理" }
    }

    const res = await getTodayPharmacistCount({ pharmacistNos }, db);
    if (!res.success) {
      return res
    }
    const { data: [countMap] } = res;
    const auditCount = countMap && Array.isArray(countMap.auditCount) ? countMap.auditCount : [];
    const auditedCount = countMap && Array.isArray(countMap.auditedCount) ? countMap.auditedCount : [];
    const list = nos.map(item => {
      const audit = auditCount.find(i => i._id === item.doctorNo);
      const audited = auditedCount.find(i => i._id === item.doctorNo);
      const auditNum = audit ? audit.count : 0;
      const auditedNum = audited ? audited.count : 0;
      return {
        pharmacistNo: item.doctorNo,
        doctorNo: item.doctorNo,
        pharmacistName: item.doctorName,
        doctorName: item.doctorName,
        weight: new BigNumber(auditNum)
          .plus(new BigNumber(auditedNum).dividedBy(1000000))//权重计算 已经审核过的权重占比小一点
          .toNumber(),
      }
    }).sort((a, b) => a.weight - b.weight); // 按照权重升序排序
    const [{ weight }] = list;
    const lightList = list.filter(i => i.weight === weight); // 取出权重最小的药师
    const randomIndex = Math.floor(Math.random() * lightList.length); // 随机选取一个药师
    const pharmacist = lightList[randomIndex];
    return { success: true, message: "获取推荐药师成功", data: pharmacist }
  } catch (e) {
    return {
      success: false,
      message: e.message || "获取推荐药师失败",
    };
  }
}

async function getTodayPharmacistCount(ctx) {
  const pharmacistNos = Array.isArray(ctx.pharmacistNos) ? ctx.pharmacistNos.filter(i => typeof i === 'string') : [ctx.pharmacistNos];
  try {

    const res = await db.collection("diagnostic-record").aggregate([
      { $match: { pharmacistNo: { $in: pharmacistNos }, createTime: { $gt: dayjs().startOf('day').valueOf(), $lte: dayjs().endOf('day').valueOf() } } },
      {
        $facet: {
          "auditCount": [
            { $match: { status: status.init } },
            { $group: { _id: "$pharmacistNo", count: { $sum: 1 } } }
          ],
          "auditedCount": [
            { $match: { status: { $in: [status.pass, status.unpass] } } },
            { $group: { _id: "$pharmacistNo", count: { $sum: 1 } } }
          ]
        }
      }
    ]).toArray()
    return { success: true, message: "查询成功", data: res };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}


async function getDoctorRxStats(ctx) {
  const { doctorCodes, startDate, endDate } = ctx;
  const query = {};
  if (Array.isArray(doctorCodes)) {
    query.doctorCode = { $in: doctorCodes };
  }
  if (startDate && dayjs(startDate).isValid()) {
    query.createTime = { $gte: dayjs(startDate).startOf('day').valueOf() };
  }
  if (endDate && dayjs(endDate).isValid()) {
    query.createTime = { ...(query.createTime || {}), $lte: dayjs(endDate).endOf('day').valueOf() };
  }
  if (typeof ctx.orderSource === 'string' && ctx.orderSource.trim() !== '') {
    query.orderSource = ctx.orderSource;
  }
  console.log("查询医生处方统计", query);
  try {
    const res = await db.collection("diagnostic-record").aggregate([
      { $match: query },
      {
        $group: {
          _id: '$doctorCode',
          doctorName: { $first: '$doctorName' },
          count: { $sum: 1 },
          passCount: { $sum: { $cond: [{ $eq: ['$status', status.pass] }, 1, 0] } },
          passInvalidCount: { $sum: { $cond: [{ $eq: ['$status', status.passInvalid] }, 1, 0] } }
        }
      }
    ], { allowDiskUse: true }).toArray()
    return { success: true, message: "查询成功", data: res };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getPharmacistRxStats(ctx) {
  const { pharmacistNos, startDate, endDate } = ctx;
  const query = {};
  if (Array.isArray(pharmacistNos)) {
    query.pharmacistNo = { $in: pharmacistNos };
  }
  if (startDate && dayjs(startDate).isValid()) {
    query.createTime = { $gte: dayjs(startDate).startOf('day').valueOf() };
  }
  if (endDate && dayjs(endDate).isValid()) {
    query.createTime = { ...(query.createTime || {}), $lte: dayjs(endDate).endOf('day').valueOf() };
  }
  try {
    const res = await db.collection("diagnostic-record").aggregate([
      { $match: query },
      {
        $group: {
          _id: '$pharmacistNo',
          doctorName: { $first: '$pharmacist' },
          count: { $sum: 1 },
          auditCount: { $sum: { $cond: [{ $in: ['$status', [status.pass, status.invalid, status.passInvalid]] }, 1, 0] } },
          expiredCount: { $sum: { $cond: [{ $eq: ['$status', status.expired] }, 1, 0] } },
        }
      }
    ]).toArray()
    const rejectRes = await db.collection("pharmacist-reject-record").aggregate([
      { $match: query },
      {
        $group: {
          _id: '$pharmacistNo',
          doctorName: { $first: '$pharmacist' },
          rejectCount: { $sum: 1 }
        }
      }
    ]).toArray();
    return { success: true, message: "查询成功", data: res, rejectRes };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}


async function checkRxUploadStatus(ctx) {
  const { orderId, registerId, patientId } = ctx;
  if (typeof orderId !== 'string' || orderId.trim() === '') {
    return { success: false, message: "订单号不能为空" };
  }
  if (typeof registerId !== 'string' || registerId.trim() === '') {
    return { success: false, message: "挂号号不能为空" };
  }
  if (typeof patientId !== 'string' || patientId.trim() === '') {
    return { success: false, message: "患者id不能为空" };
  }
  try {
    const total = await db.collection("diagnostic-record").countDocuments({
      orderId,
      patientId,
      medOrgOrderNo: registerId,
    })
    if (total === 0) {
      return { success: false, message: "处方不存在" };
    }
    const { status } = await zytHis({
      type: "hlwPrescriptionOrderStatus",
      orderno: registerId,
      patientId,
    });
    if (status === '0') {
      return { success: true, message: "正在获取订单信息，请等待1分钟后再发起退费申请!" };
    } else if (['1', '2', '3'].includes(status)) {
      return { success: true, message: "处方已上传或者已经作废" };
    }
    return { success: false, message: "处方上传状态未知，请稍后再试" };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }

}