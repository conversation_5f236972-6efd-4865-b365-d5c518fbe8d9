const { isConnected, getDatabase } = require("../mongodb");
const logger = require("./logger");

/**
 * 检查数据库连接健康状态
 * @returns {Promise<boolean>} 数据库是否健康
 */
async function checkDatabaseHealth() {
  try {
    if (!isConnected()) {
      logger.warn("数据库连接状态异常");
      return false;
    }
    
    // 尝试ping几个关键数据库
    const databases = ["admin", "corp", "Internet-hospital"];
    
    for (const dbName of databases) {
      try {
        const db = await getDatabase(dbName);
        await db.admin().ping();
      } catch (error) {
        logger.error(`数据库 ${dbName} 连接检查失败:`, error);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    logger.error("数据库健康检查失败:", error);
    return false;
  }
}

/**
 * 安全获取数据库实例，包含错误处理
 * @param {string} dbName 数据库名称
 * @returns {Promise<Object>} 数据库实例
 */
async function safeGetDatabase(dbName) {
  try {
    if (!isConnected()) {
      throw new Error("数据库连接已断开");
    }
    
    const db = await getDatabase(dbName);
    
    // 测试连接
    await db.admin().ping();
    
    return db;
  } catch (error) {
    logger.error(`获取数据库 ${dbName} 失败:`, error);
    throw new Error(`数据库连接失败: ${error.message}`);
  }
}

/**
 * 数据库操作包装器，自动处理连接错误
 * @param {Function} operation 数据库操作函数
 * @param {string} operationName 操作名称（用于日志）
 * @returns {Function} 包装后的操作函数
 */
function withDatabaseErrorHandling(operation, operationName = "数据库操作") {
  return async (...args) => {
    try {
      return await operation(...args);
    } catch (error) {
      logger.error(`${operationName}失败:`, error);
      
      // 检查是否是数据库连接相关的错误
      if (error.message.includes("collection") || 
          error.message.includes("database") ||
          error.message.includes("connection")) {
        throw new Error(`数据库连接错误: ${error.message}`);
      }
      
      throw error;
    }
  };
}

module.exports = {
  checkDatabaseHealth,
  safeGetDatabase,
  withDatabaseErrorHandling
}; 