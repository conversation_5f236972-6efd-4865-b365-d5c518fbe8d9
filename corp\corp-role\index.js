const common = require("../../common.js"); // 引入根目录common.js
let db = null;

exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case "getRoles":
      return await exports.getRoles(content);
    case "getRolesByRoleId":
      return await exports.getRolesByRoleId(content);
    case "addRole":
      return await exports.addRole(content);
    case "updateRole":
      return await exports.updateRole(content);
    case "deleteRole":
      return await exports.deleteRole(content);
    case "updateRolesCorpId":
      return await exports.updateRolesCorpId(content);
    case "checkRoleExistByIds":
      return await exports.checkRoleExistByIds(content);
  }
};

// 获取角色列表
exports.getRoles = async (context) => {
  try {
    const { corpId } = context;
    const query = { corpId };
    const list = [query];
    if (context.showOptions) { // 如果需要显示选项
      list.push({ projection: { _id: 1, roleName: 1 } });
    }

    const res = await db.collection("sys-role").find(...list).toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 根据角色ID获取角色列表
exports.getRolesByRoleId = async (context) => {
  try {
    const { corpId, roleIds } = context;
    const res = await db
      .collection("sys-role")
      .find({ corpId, _id: { $in: roleIds } })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 新增角色
exports.addRole = async (context) => {
  try {
    const { params } = context;
    params._id = common.generateRandomString(24); // 生成随机字符串作为_id
    const res = await db.collection("sys-role").insertOne(params);
    return {
      success: true,
      message: "新增成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 更新角色
exports.updateRole = async (context) => {
  try {
    const { id, params } = context;
    const res = await db
      .collection("sys-role")
      .updateOne({ _id: id }, { $set: params });
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 删除角色
exports.deleteRole = async (context) => {
  try {
    const { id } = context;
    const res = await db.collection("sys-role").deleteOne({ _id: id });
    return {
      success: true,
      message: "删除成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.updateRolesCorpId = async ({ corpId, openCorpId }) => {
  try {
    let role = await db
      .collection("sys-role")
      .findOne({ corpId, roleId: "admin" });
    if (role) {
      await db
        .collection("sys-role")
        .updateOne({ _id: role._id }, { $set: { corpId: openCorpId } });
      return {
        success: true,
        message: "更新成功",
      };
    } else {
      return {
        success: false,
        message: "未找到管理员角色",
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 检查角色是否存在
exports.checkRoleExistByIds = async (ctx) => {
  const { roleId, ids, corpId } = ctx;
  if (!roleId || !Array.isArray(ids) || ids.length === 0 || !corpId)
    return { success: false, message: "参数错误" };
  try {
    const count = await db
      .collection("sys-role")
      .find({ corpId, roleId, _id: { $in: ids } })
      .count();
    return { success: true, message: "查询成功", exist: count > 0, count };
  } catch (error) {
    return { success: false, message: error.message };
  }
};
