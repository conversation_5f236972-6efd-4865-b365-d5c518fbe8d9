const zytHis = require("./zyt-his");
const drugInfo = require("./drug-info");
const patientDescription = require("./patient-description");
const tencentIM = require("./tencent-im");
const hlwDoctor = require("./hlw-doctor");
const hlwReg = require("./hlw-reg/hlw-reg");
const hlwDiagnosis = require("./hlw-diagnosis");
const consultOrder = require("./consult-order");
const diagnosticRecord = require("./diagnostic-record");
const beijingCa = require("./beijing-ca");
const chatRecord = require("./chat-record");
const drugStore = require("./drug-store");
const statistics = require("./statistics");
const chargeItem = require("./charge-item");
const downloadXlsx = require("./download-xlsx");
const hlwTimeDuration = require("./hlw-time-duration");
const hlwConfig = require("./hlw-config");

const stats = require("./stats");

module.exports = async (item, db, res) => {
  switch (item.type) {
    case "getHisCustomer":
    case "addHisCustomer":
    case "registration":
    case "getPayStatus":
    case "hlwRefund":
    case "hlwuploadprescription":
    case "hlwPrescriptionOrderStatus":
    case "getRecent90daysDrugRecord":
      return await zytHis(item, db);
    case "getDrugInfo":
    case "deleteDrugInfo":
    case "updateDrugInfo":
    case "addDrugInfo":
    case "getHlwDrugInfo":
    case "editHlwDrugInfo":
    case "addHlwDrugInfo":
    case "setHlwDrugOnSale":
    case "importHlwDrugInfo":
      return await drugInfo(item, db);
    case "addPatientDescription":
    case "deletePatientDescription":
    case "updatePatientDescription":
    case "getPatientDescription":
      return await patientDescription(item, db);
    case "sendSystemNotification":
    case "generateUserSig":
    case "setUserProfileOrderStatys":
    case "setUserProfile":
      return await tencentIM(item, db);
    case "getHlwDoctorList":
    case "getHlwPersonList":
    case "deleteHlwDoctor":
    case "updateHlwDoctor":
    case "addHlwDoctor":
    case "getRandomOnlineDoctor":
    case "isDoctorOnline":
    case "getAllDoctorNos":
    case "getRecommendDoctor":
    case "getOptimalRecommendDoctor":
    case "setPharmacistOnlineStatus":
    case "getPharmacistOnlineStatus":
    case "addHlwPerson":
    case "updateHlwPerson":
    case "deleteHlwPerson":
    case "updateHlwPersonDeptInfo":
      return await hlwDoctor(item, db);
    case "getDiagnosisList":
    case "deleteHlwDiagnosis":
    case "updateHlwDiagnosis":
    case "addHlwDiagnosis":
    case "updateHlwDiagnosisCategory":
      return await hlwDiagnosis(item, db);
    case "getConsultOrder":
    case "updateConsultOrder":
    case "deleteConsultOrder":
    case "addConsultOrder":
    case "addDrugStoreConsultOrder":
    case "updateConsultOrderStatus":
    case "getPatientOrder":
    case "getPatientOrderList":
    case "getDrugStoreOrderList":
    case "refundConsultOrder":
    case "acceptConsultOrder":
    case "completeConsultOrder":
    case "finishConsultOrder":
    case "startConsultOrder":
    case "refreshOrderPayStatus":
    case "cancelConsultOrder":
    case "doctorHasPendingOrder":
    case "serviceFinishConsultOrder":
    case "getHisStoreRegList":
    case "validHlwOrderConfig":
    case "getOrderPatientList":
    case "getHlwOrderList":
    case "applyRefundOrder":
    case "getRefundOrderList":
    case "handleRefundOrder":
      return await consultOrder(item, db);
    case "addConsultDiagnosis":
    case "getOrderDiagnosis":
    case "getAuditDiagnosisList":
    case "getAuditedDiagnosisList":
    case "auditDiagnosis":
    case "uploadDiagnosis":
    case "reUploadDiagnosticRecord":
    case "getPatientDiagnosisRecord":
    case "getPatientPassDiagnosisRecord":
    case "getLatestSubmitTime":
    case "orderHasDiagnosis":
    case "getStoreDiagnosisRecord":
    case "getAllDiagnosisRecord":
    case "getDoctorRxStats":
    case "getPharmacistRxStats":
    case "checkRxUploadStatus":
      return diagnosticRecord(item, db);
    case "caAuth":
    case "getSealImage":
    case "getCaUserInfo":
    case "startAutoSign":
    case "autoSign":
    case "closeAutoSign":
    case "autoSignVerifyCA":
      return await beijingCa(item, db);
    case "addChatMsg":
    case "getChatRecord":
      return await chatRecord(item, db);
    case "getChargeList":
    case "addHlwChargeItem":
    case "deleteHlwChargeItem":
    case "editHlwChargeItem":
      return await chargeItem(item, db)
    case "hlwStats":
      return await stats(item, db);
    case "getDrugStoreList":
    case "addDrugStore":
    case "editDrugStore":
    case "getChargeItemList":
    case "setDrugStoreStatus":
    case "getDrugStoreOptions":
      return await drugStore(item, db);
    case "getDoctorPrescriptionCount":
    case "getStoreOrderStatistics":
    case "getTimePeriodStatistics":
      return await statistics(item, db);
    case "hlwWebReg":
    case "hlwWebRegSettle":
    case "getPatientHlwRegList":
    case "hlwRegRefund":
    case "getRegPayStatus":
      return await hlwReg(item, db);
    case "downloadXlsx":
      return await downloadXlsx(item, db, res);
    case "getDoctorTimeStats":
      return await hlwTimeDuration(item, db);
    case "getHlwOrderConfig":
    case "updateHlwOrderConfig":
      return await hlwConfig(item, db);
    default:
      return {
        success: false,
        message: "未找到接口",
      };
  }
};
