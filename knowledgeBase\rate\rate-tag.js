const config = require("./default-config");
const common = require("../../common");
const rateStars = config.rateStars;
let db = null;

exports.main = async (event, DB) => {
  db = DB;
  switch (event.type) {
    case "getCorpRateConfig":
      return await exports.getCorpRateConfig(event);
    case "addRateTag":
      return await exports.addRateTag(event);
    case "deleteRateTag":
      return await exports.deleteRateTag(event);
    case "updateRateTagText":
      return await exports.updateRateTagText(event);
  }
};

/**
 * 获取企业评分配置
 * @param {string} corpId 机构id
 * @param {boolean} afterInit 是否在初始化后调用
 */
exports.getCorpRateConfig = async (context, afterInit = false) => {
  const { corpId } = context;
  if (!isValidCorpId(corpId)) {
    return { success: false, message: "机构id不能为空" };
  }

  try {
    const data = await db
      .collection("corp-rate-config")
      .aggregate([
        { $match: { corpId, rateStar: { $in: rateStars } } },
        { $group: { _id: "$rateStar", rateTags: { $push: "$$ROOT" } } },
        { $project: { _id: 0, rateStar: "$_id", rateTags: 1 } },
      ])
      .toArray();

    if (data.length > 0 || afterInit) {
      return { success: true, data };
    }

    const initRecord = await db
      .collection("corp-rate-config")
      .findOne({ corpId, type: "init" });
    if (!initRecord) {
      await initCorpRateConfig(corpId);
      return await exports.getCorpRateConfig(context, true);
    }

    return { success: true, data: [] };
  } catch (e) {
    return { success: false, message: e.message };
  }
};

/**
 * 初始化企业评分配置
 * @param {string} corpId 机构id
 */
async function initCorpRateConfig(corpId) {
  const list = config.getDefaultConfig(corpId);
  const bulkOps = list.map((item) => ({
    insertOne: { document: { ...item, _id: common.generateRandomString(24) } },
  }));

  bulkOps.push({
    insertOne: {
      document: { corpId, type: "init", _id: common.generateRandomString(24) },
    },
  });

  try {
    await db.collection("corp-rate-config").bulkWrite(bulkOps);
    return { success: true, message: "初始化企业评分配置成功" };
  } catch (error) {
    return { success: false, message: "初始化企业评分配置失败" };
  }
}

/**
 * 新增 RateTag
 * @param {string} corpId 机构id
 * @param {RateTag} rateTag 新的 RateTag 对象，包含 corpId、rateStar、text 等字段
 * @returns {Promise<{ success: boolean, message?: string }>}
 */
exports.addRateTag = async (context) => {
  try {
    const { corpId, text, rateStar } = context;
    if (!isValidCorpId(corpId)) {
      return { success: false, message: "机构id不合法" };
    }
    if (typeof text !== "string" || text.trim() === "") {
      return { success: false, message: "评价标签内容不合法" };
    }
    if (!rateStars.includes(rateStar)) {
      return { success: false, message: "评分星级不合法" };
    }
    const res = await db.collection("corp-rate-config").insertOne({
      corpId,
      text,
      rateStar,
      _id: common.generateRandomString(24),
    });
    if (res.insertedId) {
      return { success: true, id: res.insertedId, message: "新增成功" };
    } else {
      return { success: false, message: "新增失败" };
    }
  } catch (error) {
    return { success: false, message: error.message };
  }
};

/**
 * 删除 RateTag
 * @param {string} corpId 机构id
 * @param {string} rateTagId 要删除的 RateTag 的 _id
 * @returns {Promise<{ success: boolean, message?: string }>}
 */
exports.deleteRateTag = async (context) => {
  try {
    const { corpId, id } = context;
    if (!isValidCorpId(corpId)) {
      return { success: false, message: "机构id不合法" };
    }

    await db.collection("corp-rate-config").deleteOne({ _id: id });
    return { success: true };
  } catch (error) {
    return { success: false, message: error.message };
  }
};

/**
 * 修改 RateTag 的 text
 * @param {string} corpId 机构id
 * @param {string} id 要修改的 RateTag 的 _id
 * @param {string} text 新的 text 值
 * @returns {Promise<{ success: boolean, message?: string }>}
 */
exports.updateRateTagText = async (context) => {
  try {
    const { corpId, id, text } = context;
    if (!isValidCorpId(corpId)) {
      return { success: false, message: "机构id不合法" };
    }
    if (typeof text !== "string" || text.trim() === "") {
      return { success: false, message: "评价标签内容不合法" };
    }

    await db
      .collection("corp-rate-config")
      .updateOne({ _id: id }, { $set: { text } });
    return { success: true, message: "修改成功" };
  } catch (error) {
    return { success: false, message: error.message };
  }
};

function isValidCorpId(corpId) {
  return typeof corpId === "string" && corpId.trim() !== "";
}
