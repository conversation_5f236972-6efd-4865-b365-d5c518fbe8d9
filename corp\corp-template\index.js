let db = null;
exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case "getCorpTemplate":
      return await exports.getCorpTemplate(content);
    case "getCurrentTemplate":
      return await exports.getCurrentTemplate(content);
    case "getTemplateGroup":
      return await exports.getTemplateGroup(content);
    case "updateCorpTemplateStatus":
      return await exports.updateCorpTemplateStatus(content);
    case "updateCorpTemplate":
      return await exports.updateCorpTemplate(content);
    case "getFilterFieldCorpTemplate":
      return await exports.getFilterFieldCorpTemplate(content);
  }
};
// 获取企业模板
exports.getCorpTemplate = async (content) => {
  const { corpId, env } = content;
  try {
    const data = await db
      .collection("corp-template")
      .find({ corpId })
      .toArray();
    return {
      message: "获取成功",
      data,
      success: true,
    };
  } catch (error) {
    return {
      message: "获取失败",
      data: null,
      success: false,
    };
  }
};

// 获取当前模板
exports.getCurrentTemplate = async (content) => {
  const { corpId, templateType, env } = content;
  if (!corpId || !templateType) return { success: false, message: "参数错误" };
  try {
    const data = await db
      .collection("corp-template")
      .findOne({ corpId, templateType });
    if (data) {
      return {
        message: "获取成功",
        data,
        success: true,
      };
    }
    return {
      message: "未查询到模板",
      success: false,
    };
  } catch (error) {
    return {
      message: "获取失败",
      data: null,
      success: false,
    };
  }
};

// 获取模板组
exports.getTemplateGroup = async (content) => {
  const { env, corpId, parentType } = content;
  if (!corpId || !parentType) return { success: false, message: "参数错误" };
  try {
    const data = await db
      .collection("corp-template")
      .find({ corpId, parentTemplateType: parentType })
      .project({ templateType: 1, name: 1, templateStatus: 1, _id: 0 })
      .toArray();
    if (data.length) {
      return {
        message: "获取成功",
        data,
        success: true,
      };
    }
    return {
      message: "未查询到模板",
      success: false,
    };
  } catch (error) {
    return {
      message: "获取失败",
      data: null,
      success: false,
    };
  }
};

// 更新企业模板状态
exports.updateCorpTemplateStatus = async (content) => {
  const { corpId, templateType, templateStatus } = content;
  try {
    const res = await db
      .collection("corp-template")
      .updateOne({ corpId, templateType }, { $set: { templateStatus } });
    return {
      message: "更新成功",
      data: res,
      success: true,
    };
  } catch (error) {
    return {
      message: "更新失败",
      success: false,
    };
  }
};

// 更新企业模板的单个属性
exports.updateCorpTemplateFiled = async (content) => {
  const { env, corpId, templateType, field, value } = content;
  try {
    const res = await db
      .collection("corp-template")
      .updateOne({ corpId, templateType }, { $set: { [field]: value } });
    return {
      message: "更新成功",
      data: res,
      success: true,
    };
  } catch (error) {
    return {
      message: "更新失败",
      success: false,
    };
  }
};

// 更新企业模板
exports.updateCorpTemplate = async (content) => {
  const { env, corpId, templateType, changeValue } = content;
  try {
    let templateList = [];
    if (Array.isArray(changeValue)) {
      templateList = changeValue;
    } else {
      const {
        name,
        title,
        fieldStatus,
        inputType,
        operateType,
        required,
        sort,
        systemFieldName,
        type,
        appendText,
        range,
        wordLimit,
        limit,
        referenceValue,
        referenceField,
        defaultTitle,
      } = changeValue;

      let newValue = {
        name,
        title,
        fieldStatus,
        inputType,
        operateType,
        required,
        sort,
        systemFieldName,
        type,
        appendText,
        range,
        wordLimit,
        limit,
        referenceValue,
        referenceField,
        defaultTitle,
      };

      if (type === "input" && !newValue.wordLimit) newValue.wordLimit = 20;
      if (type === "textarea" && !newValue.wordLimit) newValue.wordLimit = 200;
      if (!newValue.name)
        return { message: "模板名称不能为空", success: false };
      if (!newValue.title)
        return { message: "模板属性不能为空", success: false };

      const templateData = await db
        .collection("corp-template")
        .findOne({ corpId, templateType });
      templateList = templateData.templateList;

      const index = templateList.findIndex((item) => item.title === title);
      if (index !== -1) {
        templateList[index] = newValue;
      } else {
        templateList.push(newValue);
      }
    }

    const res = await db
      .collection("corp-template")
      .updateOne({ corpId, templateType }, { $set: { templateList } });

    return {
      message: "更新成功",
      data: res,
      success: true,
    };
  } catch (error) {
    return {
      message: "更新失败",
      success: false,
    };
  }
};

exports.getFilterFieldCorpTemplate = async (ctx) => {
  const { corpId, tempQuery } = ctx;
  const templateTypes =
    Object.prototype.toString.call(tempQuery) === "[object Object]"
      ? Object.keys(tempQuery)
      : [];
  if (!corpId || templateTypes.length === 0)
    return { success: false, message: "参数错误" };
  try {
    const list = await db
      .collection("corp-template")
      .find({ corpId, templateType: { $in: templateTypes } })
      .toArray();
    const res = list
      .map((i) => {
        if (Array.isArray(tempQuery[i.templateType])) {
          const titleList = tempQuery[i.templateType];
          const templateList = Array.isArray(i.templateList)
            ? i.templateList.filter(
                (i) =>
                  i.fieldStatus !== "disable" && titleList.includes(i.title)
              )
            : [];
          return {
            name: i.name,
            templateType: i.templateType,
            templateList,
            service: i.service,
          };
        }
        return {
          name: i.name,
          templateType: i.templateType,
          templateList: i.templateList,
          service: i.service,
        };
      })
      .filter(
        (i) => Array.isArray(i.templateList) && i.templateList.length > 0
      );
    return { success: true, data: res, message: "获取成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
};
