const crypto = require('crypto');

/**
 * AES-128加密
 * @param {string} text 要加密的文本
 * @param {string} key 16位密钥
 * @returns {string} Base64编码的加密结果
 */
function aesEncrypt(text, key) {
    if (!key || key.length !== 16) {
        throw new Error('AES-128密钥必须是16位字符串');
    }

    // 生成16位随机IV
    const iv = crypto.randomBytes(16);
    const keyBuffer = Buffer.from(key, 'utf8');

    const cipher = crypto.createCipheriv('aes-128-cbc', keyBuffer, iv);
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');

    // 将IV和加密数据组合，IV在前
    const ivBase64 = iv.toString('base64');
    return ivBase64 + encrypted;
}

/**
 * 生成签名
 * @param {object} params 签名参数
 * @param {string} secret 应用密钥
 * @returns {string} Base64编码的签名
 */
function generateSignature(params, secret) {
    // 构建签名字符串
    let stringToSign = `requestBody${params.requestBody}&secret=${secret}`;
    
    // 添加其他参数（按字典序）
    const sortedKeys = Object.keys(params).filter(key => key !== 'requestBody').sort();
    for (const key of sortedKeys) {
        stringToSign += `&${key}${params[key]}`;
    }
    
    // 生成HMAC-SHA256签名
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(stringToSign, 'utf8');
    return hmac.digest('base64');
}

/**
 * 生成请求头
 * @param {string} appId 应用ID
 * @param {string} method 服务方法
 * @returns {object} 请求头
 */
function generateHeaders(appId, method) {
    return {
        'X-Service-Id': 'his.provinceDataUploadService',
        'X-Service-Method': method,
        'X-Ca-Timestamp': Date.now().toString(),
        'X-Ca-Nonce': crypto.randomBytes(16).toString('hex'),
        'X-Ca-Appkey': appId,
        'X-Ca-Encryption': 'AES',
        'Content-Type': 'application/json'
    };
}

/**
 * 延迟函数
 * @param {number} ms 延迟毫秒数
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 * @param {Function} fn 要重试的函数
 * @param {number} times 重试次数
 * @param {number} delayMs 重试延迟
 */
async function retry(fn, times = 3, delayMs = 1000) {
    let lastError;
    for (let i = 0; i < times; i++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error;
            if (i < times - 1) {
                await delay(delayMs);
            }
        }
    }
    throw lastError;
}

module.exports = {
    aesEncrypt,
    generateSignature,
    generateHeaders,
    delay,
    retry
};
