const verfiy = require('./verify.js');
const { ObjectId } = require("mongodb");
const api = require("../../api.js");

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addDrugStore":
      return await addDrugStore(item);
    case "editDrugStore":
      return await editDrugStore(item);
    case "getChargeItemList":
      return await getChargeItemList(item);
    case "getDrugStoreList":
      return await getDrugStoreList(item);
    case "setDrugStoreStatus":
      return await setDrugStoreStatus(item);
    case "getDrugStoreOptions":
      return await getDrugStoreOptions(item)
  }
};

async function getDrugStoreList(item) {
  const { page = 1, pageSize = 10, storeId, drugStoreType, status, name, region } = item;
  try {
    const query = {};
    if (typeof storeId === 'string' && storeId.trim()) {
      query.store_id = storeId.trim();
    }
    if (Array.isArray(region) && region.length > 0) {
      region.forEach((item, idx) => {
        query[`region.${idx}`] = item;
      })
    }
    if (typeof drugStoreType === 'string' && drugStoreType.trim()) {
      query.type = drugStoreType.trim();
    }
    if (typeof status === 'string' && status.trim()) {
      query.status = status.trim();
    }
    if (typeof name === 'string' && name.trim()) {
      query.name = new RegExp(name.trim());
    }
    const total = await db.collection("store-list").countDocuments(query);
    const list = await db.collection("store-list")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            // 第二阶段：与 'corp' 集合进行关联
            from: "charge-item-list",
            localField: "charge_id",
            foreignField: "code",
            as: "chargeItems",
          },
        },                 // 根据查询条件过滤数据
        { $sort: { statusCode: -1, createTime: -1, _id: -1 } },
        { $skip: (page - 1) * pageSize },     // 跳过前面几页的数据
        { $limit: pageSize }               // 限制每页的数据量
      ])
      .toArray();
    return {
      success: true,
      message: "查询成功",
      list,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

async function addDrugStore(ctx) {
  const { params } = ctx;
  try {
    const [pass, res] = await verfiy(params || {});
    if (pass) {
      const total = await db.collection("store-list").countDocuments({ store_id: res.store_id });
      if (total > 0) {
        return { success: false, message: '店铺ID已存在' }
      }
      const charge = await db.collection("charge-item-list").findOne({ code: res.charge_id });
      if (!charge) {
        return { success: false, message: '收费项不存在' }
      }
      res.createTime = Date.now();
      if (res.accountId) {
        const res1 = await api.getCorpApi({ type: 'updateAccountDrugStoreId', corpId: ctx.corpId, drugStoreId: res.store_id, accountId: res.accountId })
        if (!res1.success) {
          return { success: false, message: res1.message || '设置关联账户失败' }
        }
      }
      await db.collection("store-list").insertOne(res);
      return { success: true, message: '新增店铺成功' }
    }
    return { success: false, message: res || '新增店铺失败' }
  } catch (e) {
    return { success: false, message: e.message || '新增店铺失败' }
  }
}

async function editDrugStore(ctx) {
  const { params, id } = ctx;
  if (!id) { return { success: false, message: 'id不能为空' } }
  try {
    const [pass, res] = await verfiy(params || {});
    if (pass) {
      const store = await db.collection("store-list").findOne({ _id: new ObjectId(id) }, { projection: { accountId: 1, store_id: 1 } });
      if (!store) {
        return { success: false, message: '店铺不存在' }
      }
      const total = await db.collection("store-list").countDocuments({ store_id: res.store_id, _id: { $ne: new ObjectId(id) } });
      if (total > 0) {
        return { success: false, message: '店铺ID重复' }
      }
      const charge = await db.collection("charge-item-list").findOne({ code: res.charge_id });
      if (!charge) {
        return { success: false, message: '收费项不存在' }
      }
      const storeIdChange = store.store_id !== res.store_id;
      const accountIdChange = store.accountId !== res.accountId;
      if (storeIdChange || accountIdChange) {
        const payload = { type: 'updateAccountDrugStoreId', corpId: ctx.corpId, drugStoreId: res.store_id }
        if (storeIdChange) {
          payload.oldDrugStoreId = store.store_id;
        }
        if (accountIdChange) {
          payload.oldAccountId = store.accountId;
          payload.accountId = res.accountId;
          
        }
        const res2 = await api.getCorpApi(payload);
        if (!res2.success) {
          return { success: false, message: res2.message || '设置关联账户失败' }
        }
      }
      await db.collection("store-list").updateOne({ _id: new ObjectId(id) }, { $set: res });
      return { success: true, message: '修改店铺成功' }
    }
    return { success: false, message: res || '修改店铺失败' }
  } catch (e) {
    return { success: false, message: e.message || '修改店铺失败' }
  }
}

async function getChargeItemList() {
  try {
    const list = await db.collection("charge-item-list").find().limit(100).sort({ fee: 1 }).toArray();
    return {
      success: true,
      message: "查询收费项成功",
      list,
    }
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询收费项失败",
    };
  }
}

async function setDrugStoreStatus(ctx) {
  const { ids, status } = ctx;
  if (!Array.isArray(ids) || ids.length === 0) { return { success: false, message: 'id不能为空' } }
  if (typeof status !== 'boolean') {
    return { success: false, message: '状态参数错误' }
  }
  try {
    const data = status ? { status: '正常营业', statusCode: 1 } : { status: '停用', statusCode: 0 };
    const res = await db.collection("store-list").updateMany({ _id: { $in: ids.map(i => new ObjectId(i)) } }, { $set: data });
    return { success: true, message: `${status ? '启用' : '停用'}店铺成功`, res }
  } catch (e) {
    return { success: false, message: e.message || '停用店铺失败' }
  }
}

async function getDrugStoreOptions(ctx) {
  try {
    const list = await db.collection('store-list').find({}, { projection: { name: 1, store_id: 1, _id: 1 } }).toArray();
    return { success: true, message: "查询成功", list }
  } catch (e) {
    return { success: false, message: e.message }
  }
}