const serviceRecord = require("./service-record");
const statistics = require("./todo-statistics");
const managementPlan = require("./management-plan");
const managementPlanTask = require("./management-plan/task");
const memberPlan = require("./management-plan/member-plan");
const customer = require("./customer");
const todoEvents = require("./to-do-events");
const todoTrigger = require("./todo-trigger");

exports.main = async (event, db) => {
  switch (event.type) {
    case "getEventsRecords":
    case "batchUpdateTodo":
    case "getEventsCount":
    case "getTodoById":
    case "setTodoExecutor":
    case "setTodoStatus":
    case "createEvents":
    case "updateTaskTodo":
    case "updateTaskTodoResult":
    case "batchAddEvent":
    case "updateEvent":
    case "createTodo":
    case "removeTodo":
    case "updateRemindFillEvnet":
    case "batchUpdateToDoAndManagePlan":
    case "getMemberIsTodoAndPlan":
    case "getCorpToDolist":
    case "updatePlannedExecutionTime":
    case "getCustomerTodos":
      return await todoEvents.main(event, db);
    case "statisticsEvents":
    case "statisticsEventsByTeamId":
    case "statisticsUserEventsByTeamId":
      return await statistics.main(event, db);
    case "addServiceRecord":
    case "updateServiceRecord":
    case "getServiceRecord":
    case "getLastServiceTime":
    case "removeServiceRecord":
    case "getServiceRecordCountToday":
    case "getCustomerServiceRecord":
    case "getTodayServiceCustomerCount":
    case "getCustomerDayServiceCount":
    case "updateCustomerDayServiceCount":
    case "getCorpServiceRecordCount":
    case "getCorpServicePointsList":
    case "getServicePointsStatistics":
    case "customerTransferRecord":
      return await serviceRecord.main(event, db);
    case "getManagementPlan":
    case "createManagementPlan":
    case "updateManagementPlan":
    case "updateManagementPlanTask":
    case "addManagementPlanTask":
    case "removeManagementPlan":
    case "removeManagementPlanTask":
    case "getManagementPlanById":
    case "executeManagementPlan":
    case "stopManagementPlan":
    case "addManageMentPlanCate":
    case "deleteManageMentPlanCate":
    case "getManageMentPlanCateList":
    case "updateManageMentPlanCate":
    case "sortManageMentPlanCate":
    case "executeManagementPlanTodo":
      return await managementPlan.main(event, db);
    case "getPlanTask":
    case "createPlanTask":
    case "removePlanTask":
    case "updatePlanTask":
    case "batchUpdateManageTask":
    case "updateExecutedTaskStatus":
    case "managementPlanTaskToEvents":
      return await managementPlanTask.main(event, db);
    case "getMemberManagementPlan":
    case "updateMemberManagementPlanStatus":
    case "updateMemberMangePlanStatus":
      return await memberPlan.main(event, db);
    case "autoCreateCustomerToEnvent":
      return await customer.autoCreateCustomerToEnvent(event, db);
    case "updateManagePlanTrigger":
    case "todoCreateServiceRecord":
    case "updatExpireStatus":
    case "pushtoDoCountToCorpApp":
    case "batchCreateGroupMsgTaskByTodo":
      return await todoTrigger.main(event, db);
    default:
      return { success: false, message: "未找到对应的方法" };
  }
};
