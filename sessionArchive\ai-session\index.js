const api = require("../../api");
const youcanProgramId = "progMGvreZ_USJ3EZSgohv0IPp1P1J8SGWMe";
const recommendProgramId = "progWDvPKtYhYNp-4iRQqK4VENpgIdYqzxxI";
let db = null;
//回调数据解析
/**
 *
 * @param {*} event
 * return
 * chat_archive_audit_approved  客户同意进行聊天内容存档事件回调
 * conversation_new_message 产生会话回调通知
 * auth_knowledge_base  	知识集管理回调
 * hit_keyword   命中关键词规则通知
 * chat_archive_export_finished  会话内容导出完成通知
 */

exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "doAsyncJob":
      return await doAsyncJob(event);
    case "getSessionArchive":
      return await this.getSessionArchive(event);
    case "getRecommendResult":
      return await this.getRecommendResult(event);
    case "getSummaryResult":
      return await this.getSummaryResult(event);
    case "getEmotionResult":
      return await this.getEmotionResult(event);
    case "createEmotionTask":
      return await this.createEmotionTask(event);
    case "createRecommendTask":
      return await createRecommendTask(event);
    case "createSummaryTask":
      return await createSummaryTask(event);
    case "getDocumentList":
      return await getDocumentList(event);
  }
};

async function getDocumentList(event) {
  const { corpId } = event;
  const { success, data } = await syncCallProgram({
    program_id: youcanProgramId,
    ability_id: "document_list",
    corpId,
  });
  const { kb_info_list, errcode } = data;
  if (!success) return { success: false, message: "获取文档列表失败" };
  if (errcode !== 0)
    return { success: false, message: "获取文档列表失败", errcode };
  return { success: true, message: "获取文档列表成功", data: kb_info_list };
}

async function doAsyncJob(event) {
  const { notify_id, corpId } = event;
  const params = {};
  const { errcode, errmsg, response_data } = await syncCallProgram({
    program_id: youcanProgramId,
    ability_id: "do_async_job",
    notify_id,
    params,
    corpId,
  });
  if (errcode !== 0) {
    console.log("doAsyncJob调用失败", errcode, errmsg);
    return {
      success: false,
      message: "调用失败",
      errcode,
      errmsg,
    };
  } else {
    const data = JSON.parse(response_data);
    if (data.event_type === "conversation_new_message") {
      // 产生会话
    }
  }
}

// 获取会话存档数据
exports.getSessionArchive = async (event) => {
  const { corpId, cursor, token, limit = 200 } = event;
  let params = {
    limit,
  };
  if (cursor) {
    params.cursor = cursor;
  }
  if (token) {
    params.token = token;
  }
  const { success, data } = await syncCallProgram({
    program_id: youcanProgramId,
    ability_id: "sync_msg",
    params,
    corpId,
  });
  return success ? data : {};
};

async function createTask(event) {
  const {
    corpId,
    createTaskParams,
    createTaskabilityId = "",
    resultAbilityId = "",
  } = event;
  const res = await syncCallProgram({
    program_id: youcanProgramId,
    ability_id: createTaskabilityId,
    params: createTaskParams,
    corpId,
  });
  if (!res.success) {
    return {
      success: false,
      message: "创建任务失败",
      errcode: res.errcode,
      errmsg: res.errmsg,
    };
  }
  const { errcode, jobid } = res.data;
  if (errcode !== 0) {
    return {
      success: false,
      message: "创建任务失败",
      errcode,
    };
  } else {
    return await getTaskResult({ corpId, jobid, resultAbilityId });
  }
}
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
async function getTaskResult(event) {
  await delay(1000);
  const { corpId, jobid, resultAbilityId } = event;
  const params = {
    jobid,
  };
  let { data } = await syncCallProgram({
    program_id: youcanProgramId,
    ability_id: resultAbilityId,
    params,
    corpId,
  });
  const { errcode, errmsg, status, ...rest } = data;
  if (errcode !== 0) {
    return {
      success: false,
      message: "获取任务结果失败",
      errcode,
      errmsg,
    };
  }
  if (status === 0) {
    await delay(1000);
    return await getTaskResult(event);
  } else if (status === 1) {
    return {
      success: true,
      message: "获取任务结果成功",
      ...rest,
    };
  } else if (status === 2) {
    return {
      success: false,
      message: "获取任务结果失败",
      errcode,
      errmsg,
    };
  }
}

// 获取话术推荐结果
exports.getRecommendResult = async (event) => {
  const { corpId, magList, kbId } = event;
  if (!kbId) {
    return {
      success: false,
      message: "kbId不能为空",
    };
  }
  if (!magList || magList.length === 0) {
    return {
      success: false,
      message: "msgList不能为空",
    };
  }
  const createTaskParams = {
    msg_list: magList,
    kb_id: kbId,
  };
  let params = {
    corpId: corpId,
    createTaskParams,
    createTaskabilityId: "create_recommend_dialog_task",
    resultAbilityId: "get_recommend_dialog_result",
  };
  return await createTask(params);
};
//获取摘要提取结果
exports.getSummaryResult = async (event) => {
  const { corpId, magList } = event;
  const createTaskParams = {
    msg_list: magList,
  };
  let params = {
    corpId: corpId,
    createTaskParams,
    createTaskabilityId: "create_summary_task",
    resultAbilityId: "get_summary_result",
  };
  return await createTask(params);
};
// 获取情感分析结果
exports.getEmotionResult = async (event) => {
  const { corpId, magList } = event;
  const createTaskParams = {
    msg_list: magList,
  };
  let params = {
    corpId: corpId,
    createTaskParams,
    createTaskabilityId: "create_sentiment_task",
    resultAbilityId: "get_sentiment_result",
  };
  return await createTask(params);
};
/**
 * 专区程序SDK调用 需要通过同一接口进行调用
 * @param {String} program_id 应用关联的程序id
 * @param {String} ability_id 能力id
 * @param {String} notify_id 通知id
 * @param {Object} request_data 请求数据
 * @returns
 */
async function syncCallProgram(item) {
  let { program_id, ability_id, notify_id, params = {}, corpId } = item;
  const request_data = {
    input: {
      func: ability_id,
      func_req: params,
    },
  };
  const query = {
    program_id,
    ability_id,
    request_data,
    notify_id,
    type: "syncCallProgram",
    corpId,
  };
  return await api.getWecomApi(query);
}

/**
 * 专区程序SDK调用 需要通过同一接口进行调用
 * @param {String} program_id 应用关联的程序id
 * @param {String} ability_id 能力id
 * @param {String} notify_id 通知id
 * @param {Object} request_data 请求数据
 * @returns
 */
async function asyncCallProgram(item) {
  let { jobid, corpId } = item;
  const query = {
    jobid,
    corpId,
    type: "asyncProgramTask",
  };
  return await api.getWecomApi(query);
}
