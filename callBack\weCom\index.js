const request = require('../request');
const suiteSecretList = {
  wwb6cc689aa5923bfb: 'VK-J74UU3kQ7sP64aMizu0ZfJXpcGATh3iwXXtcYZe0',
};
const baseURL = 'https://patient.youcan365.com';
/**
 *
 * @param {*} e
 * ToUesrname
 * AgentID
 * Encrypt
 * SuiteTicket
 * InfoType 推送类型
 */
exports.getWeComData = async (e) => {
  if (e.InfoType && e.InfoType === 'suite_ticket') {
    // 获取到SuiteTicket
    await getSuiteAccessToken(e);
    // 通过 SuiteTicket 获取第三方应用 token
  } else if (e.InfoType && e.InfoType === 'create_auth') {
    if (e.SuiteId !== 'ww9ffaf7918c964aa4') await getCorpAuthCode(e);
  } else if (e.InfoType && e.InfoType === 'cancel_auth') {
    if (e.SuiteId !== 'ww9ffaf7918c964aa4') await cancelCorpAuth(e);
  } else if (e.InfoType && e.InfoType === 'conversation_new_message') {
  } else if (e.InfoType && e.InfoType === 'change_external_contact' && e.ChangeType === 'add_external_contact') {
    console.log('授权企业中配置了客户联系功能的成员添加外部联系人时', e);
    await addExternalContact(e);
    console.log('添加外部联系人回调', addRes);
  } else if (e.InfoType && e.InfoType === 'change_external_contact' && e.ChangeType === 'del_external_contact') {
    await removeWechatFriend(e);
    console.log('删除外部联系人触发', e);
  } else if (e.InfoType && e.InfoType === 'change_external_contact' && e.ChangeType === 'edit_external_contact') {
    console.log('编辑外部联系人触发', e);
    await editExternalContact(e);
    console.log('编辑外部联系人回调', editRes);
  } else if (e.InfoType && e.InfoType === 'change_external_tag') {
    console.log('编辑客户标签触发', e);
    await syncCorpTag(e);
  } else if (e.Event && e.Event === 'program_notify') {
    console.log('程序模板消息回调', e);
  }
};

async function getCorpAuthCode(e) {
  try {
    let url = `${baseURL}/getYoucanData/weCom`;
    let params = {
      type: 'getCorpAuthCode',
      authCode: e.AuthCode,
      SuiteId: e.SuiteId,
    };
    // 会话存档授权
    if (e.SuiteId === 'ww9ffaf7918c964aa4' && e.AuthCorpId) {
      url = `${baseURL}/getYoucanData/sessionArchive`;
      params = {
        type: 'createSessionArchiveAuth',
        corpId: e.AuthCorpId,
      };
    }
    const res = await request.post(url, params);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}
async function addExternalContact(e) {
  try {
    const url = `${baseURL}/getYoucanData/weCom`;
    const params = {
      type: 'addExternalContact',
      CorpID: e.AuthCorpId,
      State: e.State,
      ExternalUserID: e.ExternalUserID,
      UserID: e.UserID,
      WelcomeCode: e.WelcomeCode,
    };
    const res = await request.post(url, params);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}

async function editExternalContact(e) {
  try {
    const url = `${baseURL}/getYoucanData/weCom`;
    const params = {
      type: 'editExternalContact',
      corpId: e.AuthCorpId,
      externalUserId: e.ExternalUserID,
      userId: e.UserID,
    };
    const res = await request.post(url, params);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}

async function syncCorpTag(e) {
  try {
    const url = `${baseURL}/getYoucanData/weCom`;
    const params = {
      type: 'syncCorpTag',
      corpId: e.AuthCorpId,
      externalUserId: e.ExternalUserID,
      tagType: e.TagType,
      id: e.Id,
      changeType: e.ChangeType,
    };
    const res = await request.post(url, params);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}

async function cancelCorpAuth(e) {
  // 取消授权
  try {
    const url = `${baseURL}/getYoucanData/corp`;
    const params = {
      type: 'updateCorp',
      corpId: e.AuthCorpId,
      params: {
        permanent_code: '',
      },
    };
    const res = await request.post(url, params);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}

async function removeWechatFriend(e) {
  try {
    const url = `${baseURL}/getYoucanData/corp`;
    const params = {
      type: 'removeWechatFriend',
      corpId: e.AuthCorpId,
      external_userid: e.ExternalUserID,
      userid: e.UserID,
    };
    const res = await request.post(url, params);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}
// 获取应用token
async function getSuiteAccessToken(e) {
  try {
    const url = `${baseURL}/getYoucanData/weCom`;
    const params = {
      type: 'getSuiteAccessToken',
      SuiteId: e.SuiteId,
      SuiteTicket: e.SuiteTicket,
      suiteSecret: suiteSecretList[e.SuiteId],
      suiteType: 'suiteToken',
    };
    console.log('获取suite_access调用传参数', params);
    const res = await request.post(url, params);
    console.log('获取suite_access调用结果', res);
    return res;
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}
