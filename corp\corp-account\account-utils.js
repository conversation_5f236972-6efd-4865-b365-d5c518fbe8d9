const validator = require('../../utils/validator.js');
const { ObjectId } = require('mongodb');

const rules = [
  {
    key: 'corpId',
    validate: val => validator.verifyString(val, '机构id', 1, 30)
  },
  {
    key: 'anotherName',
    validate: val => validator.verifyString(val, '账号名称', 1, 30)
  },
  {
    key: 'mobile',
    validate: val => validator.verifyString(val, '账号名称', 1, 50)
  },
  {
    key: 'relatedHlwPeople',
    validate: val => validator.verifyString(val, '关联人员信息', 0, 50)
  },
  {
    key: 'roleIds',
    validate: val => {
      if (val && Array.isArray(val)) {
        return [true, val.filter(i => typeof i === 'string' && i.trim() !== '')];
      }
      return [true, '']
    }
  }
];


function getAccountData(params, testAll = true) {
  if (Object.prototype.toString.call(params) !== '[object Object]') {
    return { success: false, message: '参数格式不正确' };
  }
  const data = {};
  for (const rule of rules) {
    const { key, validate } = rule;
    const testField = testAll ? true : (key in params);
    if (!testField) continue;
    const value = params[key];
    const [valid, message] = validate(value);
    if (!valid) {
      return { success: false, message };
    }
    data[key] = message;
  }
  if ('mobile' in data) {
    data.userid = new ObjectId().toString();
  }
  return { data, success: true, message: '验证通过' };
}

module.exports = { getAccountData }