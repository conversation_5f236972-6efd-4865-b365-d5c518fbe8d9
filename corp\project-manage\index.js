const serviceCate = require('./cate');
const dayjs = require('dayjs');
const common = require('../../common');
let db = null;

exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case 'addProject':
      return await exports.addProject(content);
    case 'updateProject':
      return await exports.updateProject(content);
    case 'deleteProject':
      return await exports.deleteProject(content);
    case 'getProjectList':
      return await exports.getProjectList(content);
    case 'getProjectListByCateIds':
      return await exports.getProjectListByCateIds(content);
    case 'getProjectListWithCates':
      return await exports.getProjectListWithCates(content);
    case 'getProjectDiscount':
      return await exports.getProjectDiscount(content);
    case 'getProjectCreateTreatmentOrder':
      return await getProjectCreateTreatmentOrder(content);
    case 'addProjectCate':
    case 'updateProjectCate':
    case 'deleteProjectCate':
    case 'getProjectCateList':
    case 'sortProjectCate':
    case 'ifProjectCateExist':
      return await serviceCate.main(content, db);
    case 'getProjectAllCount':
      return await getProjectAllCount(content);
    case 'getProjectNames':
      return await getProjectNames(content);
  }
};

// 新增服务项目
exports.addProject = async (content) => {
  const { corpId, params } = content;
  const { projectNo } = params;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  try {
    let { total } = await db.collection('project-list').find({ corpId, projectNo }).count();
    if (total > 0) return { success: false, message: '项目编号已存在' };
    const res = await db.collection('project-list').insertOne({
      _id: common.generateRandomString(24),
      corpId,
      ...params,
      projectStatus: 'enable',
      createTime: dayjs().valueOf()
    });
    return { success: true, message: '新增成功', data: res.insertedId };
  } catch (e) {
    return { success: false, message: '新增失败' };
  }
};

// 更新服务项目
exports.updateProject = async (content) => {
  const { corpId, params, id } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  try {
    await db.collection('project-list').updateOne({ _id: id }, { $set: params });
    return { success: true, message: '更新成功' };
  } catch (error) {
    return { success: false, message: '更新失败' };
  }
};

// 删除服务项目
exports.deleteProject = async (content) => {
  const { corpId, id } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  if (!id) return { success: false, message: '服务项目id不能为空' };
  try {
    await db.collection('project-list').deleteOne({ corpId, _id: id });
    return { success: true, message: '删除成功' };
  } catch (e) {
    return { success: false, message: '删除失败' };
  }
};

// 获取服务项目列表 分页
exports.getProjectList = async (content) => {
  const { corpId, cateIds, projectName, page, pageSize, projectStatus, ids, showDepts, hasBillDept } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  let query = { corpId };
  if (typeof projectName === 'string' && projectName.trim()) {
    const str = projectName.trim().replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&');
    query.projectName = new RegExp(str, 'i');
  }
  if (Array.isArray(cateIds)) query.projectCateIdGroup = { $in: cateIds };
  if (projectStatus) query.projectStatus = projectStatus;
  if (Array.isArray(ids)) query._id = { $in: ids };
  if (hasBillDept) query['billDeptIds.0'] = { $exists: true };
  try {
    const total = await db.collection('project-list').find(query).count();
    const pages = Math.ceil(total / pageSize);
    const action = db.collection('project-list').aggregate([
      { $match: query },
      { $sort: { createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize }
    ]);
    if (showDepts) {
      action.lookup({
        from: 'dept-list',
        localField: 'deptIds',
        foreignField: '_id',
        as: 'depts'
      });
      action.lookup({
        from: 'dept-list',
        localField: 'billDeptIds',
        foreignField: '_id',
        as: 'billDepts'
      });
    }
    const data = await action.toArray();
    return { success: true, message: '获取成功', list: data, total, pages };
  } catch (e) {
    return { success: false, message: e.message || '获取失败' };
  }
};

exports.getProjectListByCateIds = async (ctx) => {
  const { corpId, cateIds } = ctx;
  if (!corpId || !Array.isArray(cateIds)) return { success: false, message: '机构id不能为空' };
  try {
    const data = await db.collection('project-list').aggregate([
      { $match: { corpId, projectCateIdGroup: { $in: cateIds }, projectStatus: 'enable' } },
      { $project: { _id: 1, projectCateIdGroup: 1, projectName: 1 } },
      { $limit: 10000 }
    ]).toArray();
    return { success: true, message: '获取成功', data };
  } catch (e) {
    return { success: false, message: e.message || '获取失败' };
  }
};

exports.getProjectListWithCates = async (content) => {
  const { corpId, cateIds, projectName, page, pageSize, projectStatus, ids, showDepts } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  let query = { corpId };
  if (typeof projectName === 'string' && projectName.trim()) {
    const str = projectName.trim().replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&');
    query.projectName = new RegExp(str, 'i');
  }
  if (Array.isArray(cateIds)) query.projectCateIdGroup = { $in: cateIds };
  if (projectStatus) query.projectStatus = projectStatus;
  if (Array.isArray(ids)) query._id = { $in: ids };
  try {
    const total = await db.collection('project-list').find(query).count();
    const pages = Math.ceil(total / pageSize);
    const action = db.collection('project-list').aggregate([
      { $match: query },
      { $sort: { createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize }
    ]);
    if (showDepts) {
      action.lookup({
        from: 'dept-list',
        localField: 'deptIds',
        foreignField: '_id',
        as: 'depts'
      });
      action.lookup({
        from: 'dept-list',
        localField: 'billDeptIds',
        foreignField: '_id',
        as: 'billDepts'
      });
    }
    action.unwind('$projectCateIdGroup')
      .lookup({
        from: 'project-cate',
        localField: 'projectCateIdGroup',
        foreignField: '_id',
        as: 'projectCate'
      })
      .project({
        _id: 1,
        corpId: 1,
        createTime: 1,
        deptIds: 1,
        price: 1,
        projectCateIdGroup: 1,
        projectName: 1,
        projectNo: 1,
        projectStatus: 1,
        projectType: 1,
        projectCateName: { $arrayElemAt: ['$projectCate.label', 0] },
        cateParentGroup: { $arrayElemAt: ['$projectCate.parentGroup', 0] },
        depts: 1,
        description: 1,
        isDiscount: 1,
        isGift: 1,
        lowestDiscount: 1,
        unit: 1,
        billDeptIds: 1,
        billDepts: 1,
        createTreatmentOrder: 1
      })
      .lookup({
        from: 'project-cate',
        localField: 'cateParentGroup',
        foreignField: '_id',
        as: 'parentCates'
      })
      .project({
        _id: 1,
        corpId: 1,
        createTime: 1,
        deptIds: 1,
        price: 1,
        projectCateId: '$projectCateIdGroup',
        projectCateIdGroup: 1,
        projectName: 1,
        projectNo: 1,
        projectStatus: 1,
        projectType: 1,
        projectCateName: 1,
        cateParentGroup: 1,
        cateParentGroupNames: {
          $map: {
            input: '$cateParentGroupData',
            as: 'parentCate',
            in: '$$parentCate.label'
          }
        },
        depts: 1,
        description: 1,
        isDiscount: 1,
        isGift: 1,
        lowestDiscount: 1,
        unit: 1,
        billDeptIds: 1,
        billDepts: 1,
        createTreatmentOrder: 1
      });
    const data = await action.toArray();
    return { success: true, message: '获取成功', list: data, total, pages };
  } catch (e) {
    return { success: false, message: e.message || '获取失败' };
  }
};

async function getProjectAllCount({ corpId, cateIds }) {
  if (!corpId) return { success: false, msg: '缺少corpId参数' };
  if (!Array.isArray(cateIds)) return { success: false, msg: '缺少cateIds参数' };
  try {
    const total = await db.collection('project-list').find({ corpId, projectCateIdGroup: { $in: cateIds } }).count();
    return { success: true, data: total };
  } catch (e) {
    return { success: false, msg: e.message };
  }
}

async function getProjectCreateTreatmentOrder(ctx) {
  const { corpId, projectNos } = ctx;
  if (!corpId) return { success: false, msg: '缺少corpId参数' };
  if (!Array.isArray(projectNos) || projectNos.length === 0) return { success: false, msg: '缺少projectNos参数' };
  try {
    const data = await db.collection('project-list').find({ corpId, projectNo: { $in: projectNos } }, {
      projection: {
        projectNo: 1,
        createTreatmentOrder: 1
      }
    }).toArray();
    return { success: true, data };
  } catch (e) {
    return { success: false, msg: e.message };
  }
}

async function getProjectNames(ctx) {
  const { corpId, ids } = ctx;
  if (!corpId || !Array.isArray(ids) || ids.length === 0) return { success: false, msg: '缺少参数', data };
  try {
    const data = await db.collection('project-list')
      .find(
        { corpId, _id: { $in: ids } },
        { projection: { projectName: 1, _id: 1 } }
      )
      .limit(ids.length || 1).toArray();
    return { success: true, data };
  } catch (e) {
    return { success: false, msg: e.message, data: [] };
  }
}

exports.getProjectDiscount = async ctx => {
  const { corpId, projectIds } = ctx;
  if (!corpId || !Array.isArray(projectIds) || projectIds.length === 0) return { success: false, msg: '缺少参数', data };
  try {
    const data = await db.collection('project-list')
      .find(
        { corpId, _id: { $in: projectIds } },
        { projection: { lowestDiscount: 1, _id: 1 } }
      )
      .limit(projectIds.length || 1).toArray();
    return { success: true, data };
  } catch (e) {
    return
  }
}