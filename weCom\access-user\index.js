const request = require("../request.js");
const api = require("../../api.js");
// 获取访问用户信息
exports.getAccessUser = async (context) => {
  try {
    const { code } = context;
    const { success, suiteToken } = await api.getCorpApi({
      type: "getSuiteToken",
    });

    if (!success) {
      return {
        success: false,
        message: "未找到有效的 suite_token",
      };
    }
    const url = `https://qyapi.weixin.qq.com/cgi-bin/service/auth/getuserinfo3rd?suite_access_token=${suiteToken}&code=${code}`;
    const res = await request.main(url, null, "GET");
    return {
      success: true,
      message: "获取成功",
      suiteToken,
      ...res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取用户信息时发生错误",
    };
  }
};
