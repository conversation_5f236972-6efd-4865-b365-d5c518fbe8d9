const frequencyList = require('./frequency.js');
const usageList = require('./usage.js');
const unitList = require('./unit');
const categoryList = require('./drug-category');
const dosageUnitList = require('./dosageUnit.js');
const validator = require('../../utils/validator.js')

const drugFieldDict = [
  { name: '药品名称', field: 'name', required: true, verify: (val, label) => validator.verifyString(val, label, 1) },
  { name: '药品规格', field: 'specification', required: true, verify: (val, label) => validator.verifyString(val, label, 1) },
  { name: '包装单位', field: 'unit', required: true, verify: (val, label) => validator.verifyListItem(val, unitList, true, label) },
  { name: '包装量', field: 'package_amount', verify: (val, label) => validateNumber(val, label, 2) },
  { name: '生产厂家', field: 'manufacturer', verify: (val, label) => validator.verifyString(val, label) },
  { name: '商品条形码', field: 'barcode', verify: (val, label) => validator.verifyString(val, label) },
  { name: '医保编码', field: 'insurance_code', required: true, verify: (val, label) => validator.verifyString(val, label, 1) },
  { name: '药品分类', field: 'category', verify: (val, label) => validator.verifyListItem(val, categoryList, false, label) },
  { name: '药品拼音码', field: 'pinyin_code', verify: (val, label) => validator.verifyString(val, label) },
  { name: '药库货品ID', field: 'product_id', verify: (val, label) => validateNumber(val, label) },
  { name: '通用名', field: 'generic_name', verify: (val, label) => validator.verifyString(val, label) },
  { name: '通用名拼音码', field: 'generic_pinyin', verify: (val, label) => validator.verifyString(val, label) },
  { name: '单次标准剂量', field: 'dosage', verify: (val, label) => validateNumber(val, label, 2) },
  { name: '剂量单位', field: 'dosage_unit', verify: (val, label) => validator.verifyListItem(val, dosageUnitList, false, label) },
  { name: '给药标准频次', field: 'frequency', verify: (val, label) => validator.verifyListItem(val, frequencyList.map(i => i.name), false, label) },
  { name: '给药标准方式', field: 'administration_method', verify: (val, label) => validator.verifyListItem(val, usageList.map(i => i.name), false, label) },
  { name: '用药标准天数', field: 'days', verify: (val, label) => validateNumber(val, label) },
  { name: '发药标准数量', field: 'recommended_quantity', verify: (val, label) => validateNumber(val, label) },
]

function validateNumber(val, label, len = 0, required = false) {
  if (!val && val !== 0 && !required) return [true, ''];
  return validator.verifyNumber(val, label, len)
}

function getDrugList(arr, verifyEveryField = true, extraInfo = {}) {
  return arr.map(drug => {
    const res = drugFieldDict.reduce((obj, item) => {
      const key = item.field;
      if (!verifyEveryField && !(key in drug)) {
        return obj
      }
      const val = typeof drug[key] === 'string' ? drug[key].trim() : drug[key];
      const [pass, res] = item.verify(val, item.name);
      if (pass) {
        obj[item.field] = res;
      } else {
        obj[item.field] = val;
        obj.msgs.push(res)
      }
      return obj
    }, { msgs: [] })
    if ('product_id' in res) {
      res.product_id_str = res.product_id.toString();
    }
    if ('frequency' in res) {
      res.freq = res.frequency;
    }
    return { ...res, ...extraInfo }
  })
}

function getDrugFromChineseKeyObj(data) {
  const rowData = {};
  Object.keys(data).forEach(key => {
    rowData[key.trim()] = data[key];
  }) 
  return drugFieldDict.reduce((obj, item) => {
    obj[item.field] = rowData[item.name] || '';
    return obj;
  }, {});
}
module.exports = {
  getDrugList,
  getDrugFromChineseKeyObj,
}