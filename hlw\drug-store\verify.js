const storeStatus = ['停用', '正常营业']; // 不要改顺序 查询药店列表时需要
const storeType = ['直营', '加盟', '其他'];

const rules = [
  {
    prop: 'name',
    test: str => verifyString(str, '店铺名称', 1, 100)
  },
  {
    prop: 'store_id',
    test: str => verifyString(str, '店铺ID', 1, 100)
  },
  {
    prop: 'type',
    test: val => storeType.includes(val) ? [true, val] : [false, '店铺类型不正确']
  },
  {
    prop: 'accountId',
    test: str => verifyString(str, '关联账户', 0, 300)
  },
  {
    prop: 'introduce',
    test: str => verifyString(str, '店铺介绍', 0, 300)
  },
  {
    prop: 'imgs',
    test: val => [true, Array.isArray(val) ? val.filter(i => typeof i === 'string' && i.trim()).slice(0, 5) : []]
  },
  {
    prop: 'region',
    test: val => {
      const region = Array.isArray(val) ? val.filter(i => typeof i === 'string' && i.trim()) : [];
      return region.length === 3 ? [true, region] : [false, '省市区信息不完整']
    }
  },
  {
    prop: 'address',
    test: str => verifyString(str, '详情地址', 0, 300)
  },
  {
    prop: 'longitude',
    test: str => verifyString(str, '经度', 0, 10)
  },
  {
    prop: 'latitude',
    test: str => verifyString(str, '纬度', 0, 10)
  },
  {
    prop: 'charge_id',
    test: str => verifyString(str, '互联网诊查费', 1, 100)
  },
  {
    prop: 'status',
    test: val => storeStatus.includes(val) ? [true, val] : [false, '店铺状态不正确']
  },
  {
    prop: 'openingHours',
    test: str => verifyString(str, '营业时间', 0, 100)
  }
]

function verifyString(str, label, min, max) {
  if (typeof str === 'string' && str.trim().length >= min && str.trim().length < max) {
    return [true, str.trim()];
  }
  if (typeof str !== 'string' && min === 0) {
    return [true, ''];
  }
  if (typeof str !== 'string') {
    return [false, `${label}格式不正确`];
  }
  if (str.trim().length === 0 && min > 0) {
    return [false, `${label}不能为空`];
  }
  if (min > 0 && str.trim().length < min) {
    return [false, `${label}不能少于${min}个字符`];
  }
  return [false, `${label}不能超过${max}个字符`]
}

module.exports = function (data = {}, testAll = true) {
  const result = {};
  for (let rule of rules) {
    if (!testAll && !(rule.prop in data)) {
      continue;
    }
    const [pass, value] = rule.test(data[rule.prop]);
    if (pass) {
      result[rule.prop] = value;
    } else {
      return [false, value];
    }
  }
  if (storeStatus.includes(result.status)) {
    result.statusCode = storeStatus.indexOf(result.status);
  }
  // 根据店铺id 生成登录工号
  if (result.store_id) {
    result.login_no = result.store_id;
  }
  return [true, result];
}