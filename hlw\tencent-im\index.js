const request = require("../../request");
var TLSSigAPIv2 = require("./TLSSigAPIv2");
const random = Math.floor(Math.random() * 1000000);
const { getConfig } = require("../utils/config");
let db = null;
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "sendSystemNotification":
      return await sendSystemNotification(item);
    case "setUserProfileOrderStatys":
      return await setUserProfileOrderStatys(item);
    case "setUserProfile":
      return await setUserProfile(item);
    case "generateUserSig":
      return generateUserSig(item);
    case "deleteSession":
      return deleteSession(item);
    case "deleteFriend":
      return deleteFriend(item);
    case "getFriendList":
      return getFriendList(item);
    default:
      return {
        success: false,
        message: "未找到请求类型",
      };
  }
};

// 指定患者向医生发送系统消息, 医生收到消息后，不会同步到发送方
/**
 * MsgBody  消息体
 * MsgBody.MsgType  TIMTextElem（文本消息）  TIMLocationElem（位置消息） TIMFaceElem（表情消息） TIMCustomElem（自定义消息） TIMSoundElem（语音消息） TIMImageElem（图像消息） TIMFileElem（文件消息） TIMVideoFileElem（视频消息）
 * MsgBody.MsgContent  消息内容
 * MsgBody.MsgContent.Text  文本消息内容
 */
async function sendSystemNotification(item) {
  const { formAccount, toAccount, msgBody, SyncOtherMachine, corpId } = item;
  console.log("发送系统通知请求体msgBody：", msgBody);
  console.log("item", item);
  const { sdkAppId, identifier, secretKey } = await getConfig(corpId);
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    SyncOtherMachine, // 若不希望将消息同步至 From_Account，则 SyncOtherMachine 填写2；若希望将消息同步至 From_Account，则 SyncOtherMachine 填写1。
    From_Account: formAccount, // 发送方
    To_Account: toAccount, // 接收方
    MsgRandom: Math.floor(Math.random() * 1000000), // 消息随机数（32位无符号整数），后台用于同一秒内的消息去重。请确保该字段填的是随机数
    MsgBody: msgBody,
    SupportMessageExtension: 1,
  };
  console.log("发送系统通知请求体：", data);
  const url = `https://console.tim.qq.com/v4/openim/sendmsg?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  try {
    const response = await request.main(url, data, "POST");
    console.log("系统通知发送成功：", response.data);
    return {
      success: true,
      message: "系统通知发送成功",
    };
  } catch (error) {
    console.error(
      "发送系统通知失败：",
      error.response ? error.response.data : error.message
    );
    return {
      success: false,
      message: "系统通知发送失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}
/**
 *
 * 用户状态 Tag_Profile_Custom_Status
 *
 */
async function setUserProfileOrderStatys(item) {
  const { formAccount, orderStatus, corpId } = item;
  const profileItem = [
    {
      Tag: "Tag_Profile_IM_Nick",
      Value: "MyNickName22111@111",
    },
  ];
  return await setUserProfile({ formAccount, profileItem, corpId });
}
async function setUserProfile(item) {
  console.log("用户资料设置求体：", item);
  const { formAccount, profileItem, corpId } = item;
  const { sdkAppId, identifier, secretKey } = await getConfig(corpId);
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    From_Account: formAccount, // 发送方
    ProfileItem: profileItem,
  };
  const url = `https://console.tim.qq.com/v4/profile/portrait_set?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  try {
    const response = await request.main(url, data, "POST");
    console.log("用户资料设置", response.data);
    return {
      success: true,
      message: "用户资料设置成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "用户资料设置失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}

// 获取好友列表

async function getFriendList(item) {
  const { fromAccount, corpId } = item;
  console.log("删除会话请求体：", item);
  const {
    sdkAppId,
    identifier,
    secretKey,
  } = await getConfig(corpId);
  console.log("删除会话请求体：", sdkAppId, identifier, secretKey);
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    From_Account: fromAccount,
    StartIndex: 0,
    StandardSequence: 0,
  };
  console.log("获取好友请求体：", data);
  const url = `https://console.tim.qq.com/v4/sns/friend_get?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  console.log("删除会话请求体：url", url);
  try {
    const response = await request.main(url, data, "POST");
    console.log("获取好友", response);
    return {
      success: true,
      message: "获取好友成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取好友失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}

// 加好友

async function addFriend(item) {
  const { fromAccount, toAccount, corpId } = item;
  console.log("请求体：", item);
  const {
    sdkAppId,
    identifier,
    secretKey ,
  } = await getConfig(corpId);
  console.log("添加会话请求体：", sdkAppId, identifier, secretKey);
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    From_Account: fromAccount,
    AddFriendItem: [
      {
        To_Account: toAccount,
        AddSource: "AddSource_Type_internal",
      },
    ],
  };
  console.log("添加好友请求体：", data);
  const url = `https://console.tim.qq.com/v4/sns/friend_add?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  console.log("添加会话请求体：url", url);
  try {
    const response = await request.main(url, data, "POST");
    console.log("添加好友", response);
    return {
      success: true,
      message: "添加好友成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取好友失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}

async function deleteSession(item) {
  const { fromAccount, toAccount, corpId } = item;
  console.log("删除会话请求体：", item);
  const {
    sdkAppId,
    identifier,
    secretKey,
  } = await getConfig(corpId);
  console.log("删除会话请求体：", sdkAppId, identifier, secretKey);
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    From_Account: fromAccount,
    To_Account: toAccount,
    Type: 1,
    ClearRamble: 1,
  };
  console.log("删除会话请求体：", data);
  const url = `https://console.tim.qq.com/v4/recentcontact/delete?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  console.log("删除会话请求体：url", url);
  try {
    const response = await request.main(url, data, "POST");
    console.log("删除会话", response);
    return {
      success: true,
      message: "删除会话成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除会话失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}

// 删除好友
async function deleteFriend(item) {
  const { fromAccount, toAccount, corpId } = item;
  console.log("删除会话请求体：", item);
  const {
    sdkAppId,
    identifier,
    secretKey,
  } = await getConfig(corpId);
  console.log("删除会话请求体：", sdkAppId, identifier, secretKey);
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    From_Account: fromAccount,
    To_Account: [toAccount],
    DeleteType: "Delete_Type_Single",
  };

  console.log("删除会话请求体：", data);
  const url = `https://console.tim.qq.com/v4/sns/friend_delete?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  console.log("删除会话请求体：url", url);
  try {
    const response = await request.main(url, data, "POST");
    console.log("删除好友", response);
    return {
      success: true,
      message: "删除会话成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除会话失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}

// 批量导入医生账号
async function importDoctorAccount(event) {
  console.log("用户资料设置求体：", item);
  const { formAccount, profileItem, corpId } = item;
  const {
    sdkAppId,
    identifier,
    secretKey,
  } = await getConfig(corpId);
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  const { data: userSig } = await generateUserSig({
    userId: identifier,
    corpId,
    sdkAppId,
    secretKey,
  });
  // 构造请求 URL
  // 构造请求体
  const data = {
    AccountList: [],
  };
  const url = `https://console.tim.qq.com/v4/im_open_login_svc/multiaccount_import?sdkappid=${sdkAppId}&identifier=${identifier}&usersig=${userSig}&random=${random}&contenttype=json`;
  try {
    const response = await request.main(url, data, "POST");
    console.log("用户资料设置", response.data);
    return {
      success: true,
      message: "用户资料设置成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "用户资料设置失败",
      error: error.response ? error.response.data : error.message,
    };
  }
}

async function generateUserSig({ userId, corpId, sdkAppId, secretKey }) {
  if (!sdkAppId || !secretKey) {
    const res = await getConfig(corpId);
    sdkAppId = res.sdkAppId;
    secretKey = res.secretKey;
  }
  if (!sdkAppId) {
    return {
      success: false,
      message: "未找到企业配置",
    };
  }
  var api = new TLSSigAPIv2.Api(sdkAppId, secretKey);
  var sig = api.genSig(userId, 86400 * 180);
  return {
    success: true,
    message: "生成成功",
    data: sig,
    sdkAppId,
  };
}
