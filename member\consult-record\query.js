const utils = require("../utils");
const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getConsultRecordCount":
      return await getConsultRecordCount(content);
    case "getConsultStageCount":
      return await getConsultStageCount(content);
  }
};

// 获取咨询总数
async function getConsultRecordCount(content) {
  let {
    corpId,
    params = {},
    triagePersonUserIds,
    receptionPersonUserIds,
    teamId,
    createDates,
  } = content;

  // 处理分诊人员ID
  if (triagePersonUserIds && Array.isArray(triagePersonUserIds) && triagePersonUserIds.length > 0) {
    params["triagePersonUserId"] = { $in: triagePersonUserIds };
  }

  // 处理接待人员ID
  if (receptionPersonUserIds && Array.isArray(receptionPersonUserIds) && receptionPersonUserIds.length > 0) {
    params["receptionPersonUserId"] = { $in: receptionPersonUserIds };
  }

  // 处理创建日期
  if (createDates && Array.isArray(createDates) && createDates.length) {
    params["createTime"] = {
      $gte: dayjs(createDates[0]).startOf("day").valueOf(),
      $lte: dayjs(createDates[1]).endOf("day").valueOf(),
    };
  }

  if (!corpId) return { success: false, message: "机构id不能为空" };

  let query = { corpId, ...params };
  if (teamId) query.teamId = teamId;

  // 查询总交易金额和交易数量
  const totalTrade = await db.collection("consult-record").aggregate([
    { $match: { ...query, tradeAmount: { $exists: true } } },
    {
      $group: {
        _id: null,
        totalTradeAmount: { $sum: "$tradeAmount" },
        tradeCount: { $sum: 1 },
      },
    },
  ]).toArray();

  // 查询总记录数
  const totalResult = await db.collection("consult-record").aggregate([
    { $match: query },
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
      },
    },
    { $unwind: "$customerInfo" },
    { $count: "totalCount" },
  ]).toArray();

  const total = totalResult.length > 0 ? totalResult[0].totalCount : 0;
  const totalTradeAmount = totalTrade.length > 0 ? totalTrade[0].totalTradeAmount : 0;
  const tradeCount = totalTrade.length > 0 ? totalTrade[0].tradeCount : 0;

  return {
    success: true,
    message: "查询成功",
    count: total,
    totalTradeAmount,
    tradeCount,
  };
}

// 获取咨询阶段统计
async function getConsultStageCount(content) {
  const { corpId, createDates, receptionPersonUserIds, teamId, visitStatus } = content;
  let params = { corpId };

  // 处理创建日期
  if (createDates && Array.isArray(createDates) && createDates.length) {
    params["createTime"] = {
      $gte: dayjs(createDates[0]).startOf("day").valueOf(),
      $lte: dayjs(createDates[1]).endOf("day").valueOf(),
    };
  }

  // 处理接待人员ID
  if (receptionPersonUserIds && Array.isArray(receptionPersonUserIds) && receptionPersonUserIds.length > 0) {
    params["receptionPersonUserId"] = { $in: receptionPersonUserIds };
  }

  if (teamId) params.teamId = teamId;
  if (visitStatus) params["visitStatus"] = { $in: visitStatus };

  const data = await db.collection("consult-record").aggregate([
    { $match: params },
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
      },
    },
    { $unwind: "$customerInfo" },
    {
      $group: {
        _id: "$consultStage",
        count: { $sum: 1 },
        tradeCount: {
          $sum: {
            $cond: [{ $ne: ["$tradeAmount", 0] }, 1, 0],
          },
        },
        visitedCount: {
          $sum: {
            $cond: [{ $eq: ["$visitStatus", "visited"] }, 1, 0],
          },
        },
        totalTradeAmount: { $sum: "$tradeAmount" },
      },
    },
  ]).toArray();

  const res = await db.collection("consult-record").find(params).toArray();

  const result = data.reduce((acc, item) => {
    acc[item._id] = {
      count: item.count,
      tradeCount: item.tradeCount,
      totalTradeAmount: item.totalTradeAmount,
      visitedCount: item.visitedCount,
    };
    acc.totals = acc.totals || {
      count: 0,
      tradeCount: 0,
      totalTradeAmount: 0,
      visitedCount: 0,
    };
    acc.totals.count += item.count;
    acc.totals.tradeCount += item.tradeCount;
    acc.totals.totalTradeAmount += item.totalTradeAmount;
    acc.totals.visitedCount += item.visitedCount;
    return acc;
  }, {});

  return {
    success: true,
    data: result,
    message: "获取成功",
    res,
  };
}
