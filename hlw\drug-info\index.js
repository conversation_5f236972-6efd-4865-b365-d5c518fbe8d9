const dayjs = require("dayjs");
const { ObjectId } = require("mongodb");

const { getDrugList } = require('./verify');
let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getDrugInfo":
      return await getDrugInfo(item);
    case "deleteDrugInfo":
      return await deleteDrugInfo(item);
    case "updateDrugInfo":
      return await updateDrugInfo(item);
    case "addDrugInfo":
      return await addDrugInfo(item);
    case "getHlwDrugInfo":
      return await getHlwDrugInfo(item);
    case "addHlwDrugInfo":
      return await addHlwDrugInfo(item);
    case "editHlwDrugInfo":
      return await editHlwDrugInfo(item);
    case "setHlwDrugOnSale":
      return await setHlwDrugOnSale(item);
    case "importHlwDrugInfo":
      return await importHlwDrugInfo(item);
  }
};
// 药品库信息
async function getDrugInfo(item) {
  let { name, _id, pinyin_code, keyword, insuranceCodes } = item;
  let fuzzyQuery = null;
  let query = { onSale: true };
  if (typeof name === "string") query.name = new RegExp(name.trim(), "i");
  if (_id) query._id = new ObjectId(_id);
  if (typeof pinyin_code === "string")
    query.pinyin_code = new RegExp(pinyin_code.trim(), "i");
  if (Array.isArray(insuranceCodes)) {
    query.insurance_code = { $in: insuranceCodes };
  }
  const page = Number.isInteger(item.page) && item.page > 0 ? item.page : 1;
  const pageSize =
    Number.isInteger(item.pageSize) && item.pageSize > 0 ? item.pageSize : 15;
  if (typeof keyword === "string" && keyword.trim()) {
    const arr = [
      { name: new RegExp(keyword.trim(), "i") },
      { barcode: keyword.trim() },
      { pinyin_code: new RegExp(keyword.trim(), "i") },
      { product_id_str: keyword.trim() },
    ];
    fuzzyQuery = { $or: arr };
  }
  const finalQuery = fuzzyQuery ? { $and: [query, fuzzyQuery] } : query;
  try {
    // 调用mongo 数据库查询
    const res = await db
      .collection("drug-info")
      .find(finalQuery)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const total = await db.collection("drug-info").countDocuments(finalQuery);
    return {
      success: true,
      message: "查询成功",
      data: res,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }
}

// 删除药品信息
async function deleteDrugInfo(item) {
  const { _id } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("drug-info").deleteOne({
      _id: new ObjectId(_id),
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}

// 更新药品信息
async function updateDrugInfo(item) {
  const { updateData, _id } = item;
  try {
    // 调用mongo 数据库更新
    const res = await db
      .collection("drug-info")
      .updateOne(
        { _id: new ObjectId(_id) },
        { $set: { ...updateData, updateTime: Date.now() } }
      );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
//新增药品信息
/**
 * pinyin_code：拼音操作码
    product_id：药店货品ID
name：名称
specification：规格
manufacturer：厂家
unit：单位
category：分类
barcode：条形码
insurance_code：医保国码
dosage：用量
dosage_unit：用量单位
frequency：频率
days：天数
administration_method：给药方式
recommended_quantity：建议销售数量
 * @param {*} item 
 * @returns 
 */
async function addDrugInfo(item) {
  const { name } = item;
  try {
    // 调用mongo 数据库新增
    const res = await db.collection("drug-info").insertOne({
      name,
      createTime: Date.now(),
    });
    return {
      success: true,
      data: res,
      message: "新增成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "新增失败",
    };
  }
}


async function getHlwDrugInfo(ctx) {
  try {
    const page = Number.isInteger(ctx.page) && ctx.page > 0 ? ctx.page : 1;
    const pageSize = Number.isInteger(ctx.pageSize) && ctx.pageSize > 0 ? ctx.pageSize : 10;
    const query = {}
    if (typeof ctx.name === "string" && ctx.name.trim()) {
      query.name = new RegExp(ctx.name.trim(), "i")
    };
    if (typeof ctx.onSale === "boolean") {
      query.onSale = ctx.onSale
    }
    if (typeof ctx.productId === 'string' && ctx.productId.trim()) {
      query.product_id_str = ctx.productId.trim()
    }
    if (typeof ctx.barcode === 'string' && ctx.barcode.trim()) {
      query.barcode = ctx.barcode.trim()
    }
    if (typeof ctx.insuranceCode === 'string' && ctx.insuranceCode.trim()) {
      query.insurance_code = ctx.insuranceCode.trim()
    }
    const res = await db.collection("drug-info").find(query).sort({ onSale: -1, createTime: -1 }).skip((page - 1) * pageSize).limit(pageSize).toArray();
    const total = await db.collection("drug-info").countDocuments(query);
    return {
      success: true,
      message: "查询成功",
      data: res,
      total,
      pages: Math.ceil(total / pageSize),
    }
  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function addHlwDrugInfo(ctx) {
  const { params = {} } = ctx;
  if (Object.prototype.toString.call(params) !== "[object Object]") {
    return { success: false, message: "参数错误" }
  }
  try {
    const peopleInfo = {
      creator: typeof ctx.operator === "string" ? ctx.operator : "",
      creatorId: typeof ctx.operatorId === "string" ? ctx.operatorId : "",
      createTime: Date.now()
    }
    const { onSale, ...data } = params;
    let newDrug = {};
    if (Object.keys(data).length) {
      const [item] = getDrugList([data], true, peopleInfo);
      const { msgs, ...drug } = item;
      if (msgs.length) {
        return { success: false, message: msgs.join(",") }
      }
      newDrug = { ...drug }
    }
    if (typeof onSale === "boolean") {
      newDrug.onSale = onSale;
    }
    newDrug.createTime = Date.now();
    const res = await db.collection("drug-info").insertOne(newDrug);
    return { success: true, message: "新增药品成功", id: res.insertedId }
  } catch (e) {
    return { success: false, message: e.message || '新增药品失败' }
  }
}

async function editHlwDrugInfo(ctx) {
  const { id, params = {} } = ctx;
  if (Object.prototype.toString.call(params) !== "[object Object]" || typeof id !== "string") {
    return { success: false, message: "参数错误" }
  }
  try {
    const { onSale, ...data } = params;
    let updateData = {};
    if (Object.keys(data).length) {
      const peopleInfo = {
        updater: typeof ctx.operator === "string" ? ctx.operator : "",
        updaterId: typeof ctx.operatorId === "string" ? ctx.operatorId : "",
        updateTime: Date.now()
      }
      const [item] = getDrugList([data], false, peopleInfo);
      const { msgs, ...drug } = item;
      if (msgs.length) {
        return { success: false, message: msgs.join(",") }
      }
      updateData = { ...drug }
    }
    if (typeof onSale === "boolean") {
      updateData.onSale = onSale;
    }
    if (Object.keys(updateData).length) {
      updateData.updateTime = Date.now();
      const res = await db.collection("drug-info").updateOne({ _id: new ObjectId(id) }, { $set: updateData });
      if (res.matchedCount === 0) {
        return { success: false, message: "未找到对应药品" }
      }
      return { success: true, message: "更新成功" }
    }
    return { success: true, message: "更新成功" }

  } catch (e) {
    return { success: false, message: e.message }
  }

}

async function setHlwDrugOnSale(ctx) {
  const { ids, onSale, updaterId, updater } = ctx;
  const _ids = Array.isArray(ids) ? ids.filter(i => typeof i === 'string' && i.trim()).map(i => new ObjectId(i.trim())) : [ids];
  if (_ids.length == 0 || typeof onSale !== "boolean" || typeof updaterId !== "string" || typeof updater !== "string" || !updaterId.trim() || !updater.trim()) {
    return { success: false, message: "参数错误" }
  }
  try {
    const res = await db.collection("drug-info").updateMany({ _id: { $in: _ids } }, { $set: { onSale, updateTime: Date.now(), updaterId, updater } });
    return { success: true, message: "更新成功", res }
  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function importHlwDrugInfo(ctx) {
  const drugs = Array.isArray(ctx.drugs) ? ctx.drugs.filter(i => typeof i === 'object' && i !== null) : [];
  const onSale = typeof ctx.onSale === "boolean" ? ctx.onSale : true;
  if (drugs.length == 0) {
    return { success: false, message: "参数错误" }
  }
  try {
    const peopleInfo = {
      creator: typeof ctx.operator === "string" ? ctx.operator : "",
      creatorId: typeof ctx.operatorId === "string" ? ctx.operatorId : "",
      createTime: Date.now(),
    }
    const datalist = getDrugList(drugs, true, peopleInfo);
    const createTime = dayjs().valueOf();
    const list = datalist.filter(i => i.msgs.length == 0).map(i => {
      const { msgs, ...drug } = i;
      drug.createTime = createTime;
      drug.onSale = onSale;
      return drug
    });
    const res = await db.collection("drug-info").insertMany(list);
    if (res.insertedCount > 0) {
      return { success: true, message: `成功导入${res.insertedCount}条数据` }
    }
    return { success: false, message: "导入失败" }
  } catch (e) {
    return { success: false, message: e.message }
  }
}