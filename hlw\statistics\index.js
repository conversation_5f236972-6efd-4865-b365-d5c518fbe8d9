let db = "";
const dayjs = require("dayjs");
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getDoctorPrescriptionCount":
      return await getDoctorPrescriptionCount(item, db);
    case "getStoreOrderStatistics":
      return await getStoreOrderStatistics(item, db);
    case "getTimePeriodStatistics":
      return await getTimePeriodStatistics(item, db);
  }
};

// 统计医生开方数量
async function getDoctorPrescriptionCount(item, db) {
  try {
    // 构建基础查询条件
    const query = {
      status: "PASS", // 只统计成功的处方
    };
    // 添加时间范围过滤条件
    if (item.endDate && item.startDate) {
      query.createTime = {
        $gte: dayjs(item.startDate).valueOf(),
        $lte: dayjs(item.endDate).valueOf(),
      };
    } else {
      // 默认查询当天数据
      const today = new Date();
      const startOfDay = new Date(today.setHours(0, 0, 0, 0)).getTime();
      const endOfDay = new Date(today.setHours(23, 59, 59, 999)).getTime();
      query.createTime = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    }
    // 添加状态过滤条件
    if (item.orderSource) {
      query.orderSource = item.orderSource;
    }
    // 进行聚合查询
    const result = await db
      .collection("diagnostic-record")
      .aggregate([
        {
          $match: query, // 应用基础查询条件
        },
        {
          $group: {
            _id: "$doctorCode", // 按医生代码分组
            doctorName: { $first: "$doctorName" }, // 获取医生姓名
            prescriptionCount: { $sum: 1 }, // 计算开方数量
          },
        },
        {
          $project: {
            _id: 0,
            doctorCode: "$_id",
            doctorName: 1,
            prescriptionCount: 1,
          },
        },
        {
          $sort: { prescriptionCount: -1 }, // 按开方数量降序排序
        },
      ])
      .toArray();
    return {
      success: true,
      message: "查询成功",
      data: result,
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询医生开方统计失败",
    };
  }
}
// 统计店铺订单数据
async function getStoreOrderStatistics(item, db) {
  try {
    // 构建基础查询条件
    let query = {
      drugStoreNo: { $exists: true, $ne: null }, // 确保drugStoreNo存在且不为null
      status: "PASS", // 只统计成功的处方
    };
    // 添加时间范围过滤条件
    if (item.endDate && item.startDate) {
      query.createTime = {
        $gte: dayjs(item.startDate).valueOf(),
        $lte: dayjs(item.endDate).valueOf(),
      };
    } else {
      // 默认查询当天数据
      const today = new Date();
      const startOfDay = new Date(today.setHours(0, 0, 0, 0)).getTime();
      const endOfDay = new Date(today.setHours(23, 59, 59, 999)).getTime();
      query.createTime = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    }
    if (item.orderSource) {
      query.orderSource = item.orderSource;
    }
    // 直接从diagnostic-record表按drugStoreNo分组查询
    const prescriptionStats = await db
      .collection("diagnostic-record")
      .aggregate([
        {
          $match: query, // 应用筛选条件
        },
        {
          $group: {
            _id: "$drugStoreNo", // 直接按drugStoreNo分组
            prescriptionCount: { $sum: 1 }, // 计算处方数量
          },
        },
        {
          $sort: { prescriptionCount: -1 }, // 按处方数量降序排序
        },
      ])
      .toArray();
    // 获取所有相关的drugStoreNo
    const storeIds = prescriptionStats.map((item) => item._id);
    // 查询store-list集合获取店铺区域信息
    const storeList = await db
      .collection("store-list")
      .find({ store_id: { $in: storeIds } })
      .toArray();
    // 创建一个store_id到区域名称的映射
    const storeMap = {};
    storeList.forEach((store) => {
      storeMap[store.store_id] = {
        areaName: store.area || "未知区域",
        storeName: store.name || "未知店铺",
        storetype: store.type,
        region: store.region,
      };
    });
    // 合并数据
    const result = prescriptionStats.map((stat) => {
      const storeInfo = storeMap[stat._id] || {
        areaName: "未知区域",
        storeName: "未知店铺",
      };
      return {
        drugStoreNo: stat._id,
        storeName: storeInfo.storeName,
        areaName: storeInfo.areaName,
        storetype: storeInfo.storetype,
        region: storeInfo.region,
        prescriptionCount: stat.prescriptionCount,
      };
    });

    return {
      success: true,
      message: "查询成功",
      data: result,
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询店铺订单统计失败",
    };
  }
}
// 统计各时间段处方数量
async function getTimePeriodStatistics(item, db) {
  try {
    // 构建基础查询条件
    const query = {
      status: "PASS", // 只统计成功的处方
    };
    if (item.orderSource) {
      query.orderSource = item.orderSource;
    }
    // 添加时间范围过滤条件
    if (item.date) {
      query.createTime = {
        $gte: dayjs(item.date).startOf("day").valueOf(),
        $lte: dayjs(item.date).endOf("day").valueOf(),
      };
    } else {
      // 默认查询当天数据
      const today = new Date();
      const startOfDay = new Date(today.setHours(0, 0, 0, 0)).getTime();
      const endOfDay = new Date(today.setHours(23, 59, 59, 999)).getTime();
      query.createTime = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    }
    // 时间段定义
    const timeSlots = [
      //   { name: "70:00-07:30", start: 0, end: 7.5 },
      { name: "07:30-08:00", start: 7.5, end: 8 },
      { name: "08:00-08:30", start: 8, end: 8.5 },
      { name: "08:30-09:00", start: 8.5, end: 9 },
      { name: "09:00-09:30", start: 9, end: 9.5 },
      { name: "09:30-10:00", start: 9.5, end: 10 },
      { name: "10:00-10:30", start: 10, end: 10.5 },
      { name: "10:30-11:00", start: 10.5, end: 11 },
      { name: "11:00-11:30", start: 11, end: 11.5 },
      { name: "11:30-12:00", start: 11.5, end: 12 },
      { name: "12:00-12:30", start: 12, end: 12.5 },
      { name: "12:30-13:00", start: 12.5, end: 13 },
      { name: "13:00-13:30", start: 13, end: 13.5 },
      { name: "13:30-14:00", start: 13.5, end: 14 },
      { name: "14:00-14:30", start: 14, end: 14.5 },
      { name: "14:30-15:00", start: 14.5, end: 15 },
      { name: "15:00-15:30", start: 15, end: 15.5 },
      { name: "15:30-16:00", start: 15.5, end: 16 },
      { name: "16:00-16:30", start: 16, end: 16.5 },
      { name: "16:30-17:00", start: 16.5, end: 17 },
      { name: "17:00-17:30", start: 17, end: 17.5 },
      { name: "17:30-18:00", start: 17.5, end: 18 },
      { name: "18:00-18:30", start: 18, end: 18.5 },
      { name: "18:30-19:00", start: 18.5, end: 19 },
      { name: "19:00-19:30", start: 19, end: 19.5 },
      { name: "19:30-20:00", start: 19.5, end: 20 },
      { name: "20:00-20:30", start: 20, end: 20.5 },
      { name: "20:30-21:00", start: 20.5, end: 21 },
      { name: "21:00-24:00", start: 21, end: 24 },
    ];

    // 获取指定时间范围内的所有处方记录
    const prescriptions = await db
      .collection("diagnostic-record")
      .find(query)
      .toArray();

    // 初始化结果对象
    const resultMap = {};
    timeSlots.forEach((slot) => {
      resultMap[slot.name] = 0;
    });

    // 统计每个时间段的处方数量
    prescriptions.forEach((prescription) => {
      const date = new Date(prescription.createTime);
      const hour = date.getHours();
      const minute = date.getMinutes();
      const decimalHour = hour + minute / 60; // 转换为小数形式的小时

      // 查找对应的时间段
      for (const slot of timeSlots) {
        if (decimalHour >= slot.start && decimalHour < slot.end) {
          resultMap[slot.name]++;
          break;
        }
      }
    });

    // 转换为数组格式
    const result = Object.entries(resultMap)
      .map(([timeSlot, count]) => ({
        timeSlot,
        count,
      }))
      .sort((a, b) => {
        // 按时间段顺序排序
        return (
          timeSlots.findIndex((slot) => slot.name === a.timeSlot) -
          timeSlots.findIndex((slot) => slot.name === b.timeSlot)
        );
      });

    return {
      success: true,
      message: "查询成功",
      data: result,
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询时间段统计失败",
    };
  }
}
