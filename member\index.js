const corpCustomer = require("./corp-customer");
const memberdefault = require("./defuat-member");
const registration = require("./registration");
const selectedChatMember = require("./selected-chat-member");
const pannedEvent = require("./plan-event");
const medicalRecord = require("./medical-record");
const customerStatistics = require("./customer-statistics");
const transferCustomers = require("./transfer-customers");
const batchOperation = require("./batch-operation");
const customerTag = require("./customer-tag");
const customer = require("./customer");
const corpGroup = require("./corp-group");
const unionidUserId = require("./unionid-userid");
const feeRecord = require("./fee-record");
const sop = require("./sop");
const sopStatistic = require("./sop/statistic");
const pointsManagement = require("./points-management");
const consultRecord = require("./consult-record");
const projectPackage = require("./project-package");
const addBillRecord = require("./bill-record");
const treatmentRecord = require("./treatment-record");

exports.main = async (context, db) => {
  switch (context.type) {
    case "getMember":
    case "getCustomerInSidebar":
    case "batchImportCustomer":
    case "getmemberCountForTeamId":
    case "getmemberAndserviceCountForUserId":
    case "getMemberInfo":
    case "getMemberForTeam":
    case "getCorpCustomer":
    case "getMemberBytags":
    case "externalUseridToMember":
    case "autoCreateMember":
    case "asyncCustomerTag":
    case "add":
    case "delete":
    case "update":
    case "getReferredPeople":
    case "updateCustomerTimeField":
    case "getCustomerByUnionId":
    case "getCustomerNamesById":
    case "getCustomersCount":
    case "getInHospitalCustomersCount":
    case "triggerSoptaskByCustomerId":
    case "addConsumeRecord":
    case "pushCustomerTeamId":
    case "updateCustomerReportPeople":
    case "getCustomerReportLog":
    case "updateCustomerPersonResponsibles":
      return await customer.main(context, db);
    case "syncCustomerTag":
      return await customerTag.main(context, db);
    case "getDefaultMember":
    case "updateDefautMember":
    case "addDefuatMember":
      return await memberdefault.main(context, db);
    case "registration":
    case "getRegistration":
    case "getRegistrationCountByUserId":
    case "getRegistrationCountByTeam":
      return await registration.main(context, db);
    case "selectedChatMember":
    case "getSelectedChatMember":
      return await selectedChatMember.main(context, db);
    case "getCustomerType":
    case "updatePannedEvent":
    case "createPannedEvent":
    case "getPannedEvent":
    case "removePannedEvent":
    case "getPannedEventById":
    case "getPannedEventResult":
    case "getTodoEventsByPannedEventId":
    case "planEventCreateTodoEvent":
      return await pannedEvent.main(context, db);
    case "addMedicalRecord":
    case "getCustomerMedicalRecord":
    case "getMedicalRecordById":
    case "updateMedicalRecord":
    case "removeMedicalRecord":
    case "getArriveTime":
    case "getMedicalRecordByUserId":
    case "mergeMedicalHisRecord":
    case "getMedicalFiledsTrend":
    case "addHealthIndicators":
    case "getHealthIndicators":
    case "getHealthIndicatorsTemplate":
      return await medicalRecord.main(context, db);
    case "getUsersMemberByTeamId":
    case "getServiceTrendByTeamId":
    case "getMemberTrendByTeamId":
    case "getServiceAndmemberTotalByTeamId":
    case "getCustomerStatistics":
    case "getTeamsServiceAndMemberCount":
    case "getStaffServiceAndMemberCount":
    case "getCustomerSourceCountSort":
    case "getCustomerGroupCountSort":
    case "getCusomterAndServiceCount":
    case "getStaffRate":
    case "getScoreRateTrend":
    case "getServiceAndmemberTotalByUserId":
    case "getTeamCustmerCount":
      return await customerStatistics.main(context, db);
    case "searchCorpCustomer":
    case "batchUpdateCustomer":
      return await corpCustomer.main(context, db);
    case "transferCustomers":
      return await transferCustomers.main(context, db);
    case "batchUpdateCustomerTeamIds":
    case "batchRemoveMember":
      return await batchOperation.main(context, db);
    case "getGroups":
    case "createGroup":
    case "updateGroup":
    case "removeGroup":
    case "addGroupIdForMember":
    case "orderTeamGroups":
    case "getGroupsByCorpGroupId":
    case "customerEnterGroup":
    case "getGroupByIds":
      return await corpGroup.main(context, db);
    case "getUnionidByExternalUserID":
    case "addCorpUserIDAndUnionid":
    case "addExternalUserIDAndUnionid":
    case "getUnionidToExternalUserid":
    case "getExternalUserIDByUnionid":
      return await unionidUserId.main(context, db);
    case "getFeeRecord":
    case "merageHisFeeRecord":
    case "feeRecordStatistics":
    case "updateFeeRecord":
    case "addFeeRecord":
    case "batchAddFeeRecord":
      return await feeRecord.main(context, db);
    case "getSopTaskList":
    case "getSopTask":
    case "createSopTask":
    case "removeSopTask":
    case "getSopFilterRule":
    case "updateSopTask":
    case "updateSopTaskExecuteUser":
    case "executeSopTask":
    case "triggerSopTask":
    case "removeCustomerSopTask":
    case "getCustomerSopTask":
    case "createCustomerSopTask":
    case "addSopCate":
    case "updateSopCate":
    case "deleteSopCate":
    case "getSopCateList":
    case "sortSopCate":
    case "updateSopTaskField":
    case "customerSopTaskTrigger":
    case "customerSopTaskAutoGroup":
      return await sop.main(context, db);
    case "getSopTaskResultList":
    // case 'getSopTaskResultStatistic':
    case "updateCustomerSopTaskStatus":
    case "updateGroupMsgTaskResult":
    case "getSopTaskResultForTag":
    case "getSopTaskResultForGroup":
    case "getGroupTaskResultList":
      return await sopStatistic.main(context, db);
    case "createPointsTask":
    case "getPointsTaskList":
    case "updatePointsTask":
    case "getPointsTask":
    case "deletePointsTask":
      return await pointsManagement.main(context, db);
    case "addConsultRecord":
    case "updateConsultRecord":
    case "deleteConsultRecord":
    case "getConsultRecord":
    case "getConsultRecordCount":
    case "getConsultStageCount":
    case "addEConsuleRecord":
    case "updateEConsuleRecord":
    case "getEConsuleRecord":
    case "getFirstEConsuleRecord":
    case "getFirstTriagePersonUserId":
      return await consultRecord.main(context, db);
    case "addBillRecord":
    case "getBillRecord":
    case "updateBillRecord":
    case "deductPorjectUsageCount":
      return await addBillRecord.main(context, db);
    case "addTreatmentRecord":
    case "getTreatmentRecord":
    case "updateTreatmentRecord":
    case "removeTreatmentRecord":
    case "treatmentRecordStatistic":
    case "getDeductRecord":
    case "addDeductRecord":
    case "updateDeductRecord":
    case "treatmentRecordStatisticByStatus":
    case "deductRecordStatistic":
    case "removeDeductRecord":
      return await treatmentRecord.main(context, db);
    case "addProjectPackage":
    case "getProjectPackageList":
    case "removeProjectPackage":
    case "setProjectPackageEnable":
    case "updateProjectPackage":
    case "addProjectPackageCate":
    case "updateProjectPackageCate":
    case "deleteProjectPackageCate":
    case "getProjectPackageCateList":
    case "sortProjectPackageCate":
      return await projectPackage.main(context, db);
    default:
      return {
        success: false,
        message: "未找到对应的操作类型",
      };
  }
};
