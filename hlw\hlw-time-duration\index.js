const dayjs = require("dayjs");

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addDoctorOrderDuration":
      return await addDoctorTime(item)
    case "addDoctorRxDuration":
      return await addDoctorRxDuration(item)
    case "getDoctorTimeStats":
      return await getDoctorTimeStats(item)
    default:
      return {
        success: false,
        message: "请求失败!",
      };
  }
};

// 记录医生的接诊时长
async function addDoctorTime(ctx) {
  const duration = Date.now() - ctx.createTime;
  if (!(duration > 0)) return;
  try {
    await db.collection("hlw-doctor-times").insertOne({
      doctorCode: ctx.doctorCode,
      doctorName: ctx.doctorName,
      orderId: ctx.orderId,
      createTime: ctx.createTime,
      acceptDuration: duration
    })
  } catch (e) { 
    console.log("addDoctorTime error", e.message);
  }
}
async function addDoctorRxDuration(ctx) {
  if (typeof ctx.orderId !== "string") return;
  try {
    const record = await db.collection("hlw-doctor-times").findOne({ orderId: ctx.orderId, rxDuration: { $exists: false } }, { projection: { _id: 1, createTime: 1 } });
    if (!record) return;
    const duration = Date.now() - record.createTime;
    if (!(duration > 0)) return;
    await db.collection("hlw-doctor-times").updateOne(
      { _id: record._id },
      { $set: { rxDuration: duration } }
    );
  } catch (e) { 
    console.log("addDoctorRxDuration error", e.message);
  }
}

async function getDoctorTimeStats(ctx) {
  const startDate = ctx.startDate && dayjs(ctx.startDate).isValid() ? dayjs(ctx.startDate).startOf("day").toDate() : 0;
  const endDate = ctx.endDate && dayjs(ctx.endDate).isValid() ? dayjs(ctx.endDate).endOf("day").toDate() : 0;
  const doctorCodes = Array.isArray(ctx.doctorCodes) ? ctx.doctorCodes : [];
  const match = { doctorCode: { $in: doctorCodes } };
  if (startDate) {
    match.createTime = { $gte: dayjs(startDate).startOf('day').valueOf() };
  }
  if (endDate) {
    match.createTime = { ...(match.createTime || {}), $lte: dayjs(endDate).endOf('day').valueOf() };
  }
  const acceptList = await db.collection("hlw-doctor-times").aggregate([
    { $match: { ...match, acceptDuration: { $gt: 0 } } },
    {
      $group: {
        _id: "$doctorCode",
        sumAccept: { $sum: "$acceptDuration" },
        sum: { $sum: 1 },
      }
    }
  ]).toArray();
  const rxtList = await db.collection("hlw-doctor-times").aggregate([
    { $match: { ...match, rxDuration: { $gt: 0 } } },
    {
      $group: {
        _id: "$doctorCode",
        sumRx: { $sum: "$rxDuration" },
        sum: { $sum: 1 },
      }
    }
  ]).toArray();
  return {
    success: true,
    acceptList,
    rxtList
  };
}