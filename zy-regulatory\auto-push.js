#!/usr/bin/env node

/**
 * 监管平台数据自动推送流程
 * 简单的自动发送流程，无需管理服务器状态
 */

// 加载环境变量
const envFile = `.env.${process.env.NODE_ENV || "development"}`;
require("dotenv").config({ path: envFile });

const services = require('./services');
const { connectToMongoDB, getDatabase, closeMongoDB } = require('../mongodb');
const dayjs = require('dayjs');

// 只使用当日数据

/**
 * 获取咨询数据 - 当日完成的订单
 */
async function getConsultData(limit = 100) {
    try {
        const db = await getDatabase("Internet-hospital");

        // 获取当日时间范围
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime();
        const endOfDay = startOfDay + 24 * 60 * 60 * 1000 - 1;

        const query = {
            createTime: { $gte: startOfDay, $lte: endOfDay },
            orderStatus: "finished", // 只推送完成的订单
            // 避免重复推送
            $or: [
                { regulatoryPushed: { $exists: false } },
                { regulatoryPushed: false }
            ]
        };

        const consultOrders = await db.collection("consult-order")
            .find(query)
            .sort({ createTime: -1 }) // 按时间倒序
            .limit(limit)
            .toArray();

        console.log(`📋 咨询数据查询: ${dayjs(startOfDay).format('MM-DD')} 完成订单 ${consultOrders.length} 条`);
        return consultOrders;
    } catch (error) {
        console.error('❌ 获取咨询数据失败:', error.message);
        return [];
    }
}

/**
 * 获取处方数据 - 当日数据，无需验证状态
 */
async function getRecipeData(limit = 100) {
    try {
        const db = await getDatabase("Internet-hospital");

        // 获取当日时间范围
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime();
        const endOfDay = startOfDay + 24 * 60 * 60 * 1000 - 1;

        const matchStage = {
            createTime: { $gte: startOfDay, $lte: endOfDay },
            status: "PASS", // 只推送审核通过的处方
            // 避免重复推送
            $or: [
                { regulatoryPushed: { $exists: false } },
                { regulatoryPushed: false }
            ]
        };

        const pipeline = [
            { $match: matchStage },
            {
                $lookup: {
                    from: "consult-order",
                    localField: "orderId",
                    foreignField: "orderId",
                    as: "consultOrder"
                }
            },
            { $sort: { createTime: -1 } }, // 按时间倒序
            { $limit: limit }
        ];

        const diagnosticRecords = await db.collection("diagnostic-record")
            .aggregate(pipeline)
            .toArray();

        console.log(`💊 处方数据查询: ${dayjs(startOfDay).format('MM-DD')} PASS状态 ${diagnosticRecords.length} 条`);
        return diagnosticRecords;
    } catch (error) {
        console.error('❌ 获取处方数据失败:', error.message);
        return [];
    }
}



/**
 * 准备咨询数据
 */
function prepareConsultData(consultOrders) {
    return consultOrders.map(order => ({
        orderId: order.orderId,
        deptName: order.deptName || '全科医学科',
        doctorNo: order.doctorCode,  // 传递doctorCode给services层处理
        idCard: order.idCard || '',
        name: order.name || '',
        sex: order.sex || '1',
        consultationType: '1',
        consultationAttribute: '1',
        answerFlag: order.orderStatus === 'finished' ? '1' : '0',
        updateTime: order.updateTime || order.createTime,
        age: order.age || 0,
        doctorName: order.doctorName || '',
        mobile: order.mobile || ''
    }));
}

/**
 * 准备处方数据
 */
function prepareRecipeData(diagnosticRecords) {
    return diagnosticRecords.map(record => {
        const consultOrder = record.consultOrder?.[0] || {};

        return {
            orderId: record.orderId,
            medOrgOrderNo: record.medOrgOrderNo || record.orderId,
            originalDiagnosis: record.originalDiagnosis,  // 传递给services层处理
            deptName: consultOrder.deptName || '全科医学科',
            doctorNo: record.doctorCode,  // 传递doctorCode给services层处理
            auditDoctorNo: record.pharmacistNo,  // 传递pharmacistNo给services层处理
            idCard: consultOrder.idCard || '',
            sex: consultOrder.sex || '1',
            createTime: record.createTime,
            updateTime: record.updateTime || record.createTime,
            orderList: (record.drugs || record.orderList || []).map(drug => ({
                drname: drug.drugName || '',                               // 药品名称 (准确配对)
                drmodel: drug.specification || '',                         // 药品规格 (准确配对)
                packUnit: drug.unit || '',                                 // 药品包装单位 (准确配对)
                admission: drug.usageName || '',                           // 药品用法 (准确配对)
                frequency: drug.frequencyName || '',                       // 用品使用频度 (准确配对)
                dosage: drug.dosage?.toString() || '',                     // 每次剂量 (准确配对，转为字符串)
                drunit: drug.dosage_unit || '',                           // 剂量单位 (准确配对)
                useDays: drug.days || 0                                    // 用药天数 (准确配对)
            })),
            diagnosisList: record.diagnosisList || [],
            diseases: consultOrder.diseases || [],
            answerFlag: record.answerFlag || '1'
        };
    });
}

/**
 * 标记数据为已推送
 */
async function markDataAsPushed(collection, orderIds) {
    try {
        const db = await getDatabase("Internet-hospital");
        const timestamp = Date.now();
        
        await db.collection(collection).updateMany(
            { orderId: { $in: orderIds } },
            { 
                $set: { 
                    regulatoryPushed: true, 
                    regulatoryPushTime: timestamp,
                    'X-Ca-Timestamp': timestamp
                } 
            }
        );
        
        console.log(`✅ 标记 ${orderIds.length} 条${collection === 'consult-order' ? '咨询' : '处方'}数据为已推送`);
    } catch (error) {
        console.error('❌ 标记数据推送状态失败:', error.message);
    }
}

/**
 * 执行自动推送 - 推送当日完成的订单
 */
async function executeAutoPush() {
    const timestamp = Date.now();
    console.log(`🚀 监管平台数据推送开始 - ${dayjs(timestamp).format('MM-DD HH:mm')}`);

    try {
        await connectToMongoDB();

        let totalSuccess = 0;
        let totalFailed = 0;
        let consultCount = 0;
        let recipeCount = 0;

        // 推送咨询数据
        try {
            const consultOrders = await getConsultData();
            consultCount = consultOrders.length;
            if (consultOrders.length > 0) {
                const preparedConsultData = prepareConsultData(consultOrders);
                // 显示关键数据信息
                const sample = preparedConsultData[0];
                console.log(`📋 咨询样本: ${sample.orderId} | 患者:${sample.name || '未知'} | 科室:${sample.deptName} | 状态:${sample.answerFlag}`);

                const consultResult = await services.uploadConsultData(preparedConsultData);
                if (consultResult.success) {
                    await markDataAsPushed('consult-order', consultOrders.map(o => o.orderId));
                    totalSuccess += consultOrders.length;
                } else {
                    console.log(`❌ 咨询推送失败: ${consultResult.message}`);
                    totalFailed += consultOrders.length;
                }
            }
        } catch (error) {
            console.error(`❌ 咨询数据推送异常: ${error.message}`);
            // 继续执行，不中断后续推送
        }

        // 推送处方数据
        try {
            const diagnosticRecords = await getRecipeData();
            recipeCount = diagnosticRecords.length;
            if (diagnosticRecords.length > 0) {
                const preparedRecipeData = prepareRecipeData(diagnosticRecords);
                // 显示关键数据信息
                const sample = preparedRecipeData[0];
                const consultOrder = diagnosticRecords[0].consultOrder?.[0] || {};
                console.log(`💊 处方样本: ${sample.orderId} | 患者:${consultOrder.name || '未知'} | 药品:${sample.orderList?.length || 0}种 | 诊断:${sample.originalDiagnosis}`);

                const recipeResult = await services.uploadRecipeData(preparedRecipeData);
                if (recipeResult.success) {
                    await markDataAsPushed('diagnostic-record', diagnosticRecords.map(r => r.orderId));
                    totalSuccess += diagnosticRecords.length;
                } else {
                    console.log(`❌ 处方推送失败: ${recipeResult.message}`);
                    totalFailed += diagnosticRecords.length;
                }
            }
        } catch (error) {
            console.error(`❌ 处方数据推送异常: ${error.message}`);
            // 继续执行，不中断后续推送
        }

        // 推送结果统计
        console.log(`📊 推送完成: 成功 ${totalSuccess} 条, 失败 ${totalFailed} 条 - ${dayjs().format('HH:mm')}`);

        return {
            success: totalFailed === 0,
            totalSuccess,
            totalFailed,
            consultCount,
            recipeCount,
            timestamp
        };

    } catch (error) {
        console.error('❌ 自动推送执行失败:', error.message);
        return {
            success: false,
            error: error.message,
            timestamp
        };
    } finally {
        await closeMongoDB();
    }
}

/**
 * 定时推送（每日指定时间）
 */
function startScheduledPush(scheduleTime) {
    if (!scheduleTime || !scheduleTime.includes(':')) {
        console.error('❌ 请指定有效的时间格式，如: 22:00');
        process.exit(1);
    }

    console.log(`� 启动定时推送，每日 ${scheduleTime} 执行`);
    scheduleDaily(scheduleTime);
    console.log('💡 按 Ctrl+C 停止定时推送');
}

/**
 * 每日指定时间执行推送
 */
function scheduleDaily(timeString) {
    const [hour, minute] = timeString.split(':').map(Number);

    function scheduleNext() {
        const now = new Date();
        const scheduledTime = new Date();
        scheduledTime.setHours(hour, minute, 0, 0);

        // 如果今天的时间已过，安排到明天
        if (scheduledTime <= now) {
            scheduledTime.setDate(scheduledTime.getDate() + 1);
        }

        const timeUntilExecution = scheduledTime.getTime() - now.getTime();
        console.log(`⏰ 下次推送时间: ${scheduledTime.toLocaleString()}`);

        const timeout = setTimeout(async () => {
            console.log(`🕙 定时推送开始 - ${new Date().toLocaleString()}`);
            try {
                await executeAutoPush();
            } catch (error) {
                console.error('定时推送执行失败:', error.message);
            }

            // 安排下一次执行
            scheduleNext();
        }, timeUntilExecution);

        // 优雅停止处理
        process.on('SIGINT', () => {
            console.log('\n📴 收到停止信号，正在关闭定时推送...');
            clearTimeout(timeout);
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            console.log('\n📴 收到终止信号，正在关闭定时推送...');
            clearTimeout(timeout);
            process.exit(0);
        });
    }

    scheduleNext();
}

// ==================== 命令行处理 ====================

function showHelp() {
    console.log('监管平台数据自动推送工具');
    console.log('');
    console.log('用法:');
    console.log('  node zy-regulatory/auto-push.js [command] [options]');
    console.log('');
    console.log('命令:');
    console.log('  push           执行单次自动推送');
    console.log('  schedule TIME  启动定时推送，每日指定时间执行');
    console.log('  --help         显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node zy-regulatory/auto-push.js push');
    console.log('  node zy-regulatory/auto-push.js schedule 22:00');
    console.log('  node zy-regulatory/auto-push.js schedule 08:30');
    console.log('');
    console.log('环境变量:');
    console.log('  NODE_ENV=development|pro|zytDev    指定运行环境');
}

async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'push';

    if (command === '--help' || command === '-h' || command === 'help') {
        showHelp();
        return;
    }

    try {
        switch (command) {
            case 'push':
                const result = await executeAutoPush();
                process.exit(result.success ? 0 : 1);
                break;

            case 'schedule':
                const scheduleTime = args[1];
                startScheduledPush(scheduleTime);
                // 保持进程运行
                break;

            default:
                console.error(`❌ 未知命令: ${command}`);
                showHelp();
                process.exit(1);
        }

    } catch (error) {
        console.error('❌ 自动推送工具启动失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
    main();
}

module.exports = {
    executeAutoPush,
    startScheduledPush,
    getConsultData,
    getRecipeData,
    prepareConsultData,
    prepareRecipeData
};
