const utils = require("../utils");
const dayjs = require("dayjs");
const staffqrcodeCate = require("./staff-qrcode-cate");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "addQrcodeCustomerCount":
      return await exports.addQrcodeCustomerCount(content);
    case "getQrcodeCustomer":
      return await exports.getQrcodeCustomer(content);
    case "getQrcodeCustomerCount":
      return await exports.getQrcodeCustomerCountForStaff(content);
    case "getAllQrcodeCustomerCount":
      return await exports.getAllQrcodeCustomerCount(content);
    case "getQrcodeStaticsticsSort":
      return await getQrcodeStaticsticsSort(content);
    case "getQrcodeCustomerStatsByCateId":
      return await getQrcodeCustomerStatsByCateId(content);
  }
};

async function getQrcodeStaticsticsSort({ corpId }) {
  try {
    const yearTimestamp = dayjs().subtract(1, "year").valueOf();
    const pageSize = 1000;
    let page = 1;
    let allData = [];

    while (true) {
      const res = await db
        .collection("qrcode-statistics")
        .aggregate([
          {
            $match: {
              corpId,
              createTime: { $gte: yearTimestamp },
              qrCodeName: { $ne: "个人二维码" },
            },
          },
          { $skip: (page - 1) * pageSize },
          { $limit: pageSize },
          { $group: { _id: "$configId", count: { $sum: 1 } } },
          {
            $lookup: {
              from: "staff-qrcode",
              localField: "_id",
              foreignField: "configId",
              as: "staff_qrcode_data",
            },
          },
          {
            $unwind: {
              path: "$staff_qrcode_data",
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              _id: 1,
              count: 1,
              qrCodeName: "$staff_qrcode_data.qrCodeName",
            },
          },
        ])
        .toArray();

      if (res.length === 0) break;
      allData = allData.concat(res);
      page++;
    }

    const sortedList = allData.sort((a, b) => b.count - a.count).slice(0, 5);

    return { success: true, message: "获取成功", list: sortedList };
  } catch (error) {
    return {
      success: false,
      message: "获取失败，发生错误。",
      error: error.message,
    };
  }
}

exports.addQrcodeCustomerCount = async (context) => {
  let { params, userId, externalUserId } = context;
  const { corpId, configId, name, qrCodeName } = params;
  console.log("context", context);
  if (!userId) return { success: false, message: "userId不能为空" };
  if (!configId) return { success: false, message: "configId不能为空" };

  try {
    const total = await db
      .collection("qrcode-statistics")
      .countDocuments({ userId, configId, externalUserId });
    if (total > 0) return { success: false, message: "已添加过该客户" };

    const query = {
      _id: common.generateRandomString(24),
      configId,
      corpId,
      userId,
      externalUserId,
      createTime: new Date().getTime(),
      name,
      qrCodeName,
    };
    await db.collection("qrcode-statistics").insertOne(query);
    return { success: true, message: "添加成功" };
  } catch (error) {
    return { success: false, message: "添加失败" };
  }
};

exports.getQrcodeCustomerCountForStaff = async (context) => {
  try {
    const { corpId, configId } = context;
    const data = await db
      .collection("qrcode-statistics")
      .aggregate([
        { $match: { corpId, configId } },
        { $group: { _id: "$userId", count: { $sum: 1 } } },
      ])
      .toArray();

    return { success: true, message: "获取成功", data };
  } catch (error) {
    return { success: false, message: error };
  }
};

exports.getAllQrcodeCustomerCount = async ({ item }) => {
  try {
    const { corpId, configId } = item;
    const allAddCustomerCount = await db
      .collection("qrcode-statistics")
      .countDocuments({ corpId, configId });
    const todayAddCustomerCount = await db
      .collection("qrcode-statistics")
      .countDocuments({
        corpId,
        configId,
        createTime: {
          $gte: dayjs().startOf("day").valueOf(),
          $lte: dayjs().endOf("day").valueOf(),
        },
      });

    return {
      success: true,
      message: "获取成功",
      data: { allAddCustomerCount, todayAddCustomerCount, configId },
    };
  } catch (error) {
    return { success: false, message: error };
  }
};

exports.getQrcodeCustomer = async (context) => {
  const { corpId, configId, dates } = context;
  const startTimestamp = dayjs(dates[0]).startOf("day").valueOf();
  const endTimestamp = dayjs(dates[1]).endOf("day").valueOf();

  try {
    const fetchData = async (page, pageSize, db) => {
      const data = await db
        .collection("qrcode-statistics")
        .find({
          corpId,
          configId,
          createTime: { $gte: startTimestamp, $lte: endTimestamp },
        })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray();
      return data;
    };
    const list = await utils.getAllData(fetchData, db);
    return { success: true, message: "获取成功", data: list };
  } catch (error) {
    return { success: false, message: error };
  }
};

async function getQrcodeCustomerStatsByCateId(ctx) {
  const { corpId, cateIds, startTime, endTime } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!Array.isArray(cateIds) || cateIds.length === 0)
    return { success: true, data: [], qrcodes: [] };

  try {
    const qrcodes = await db
      .collection("staff-qrcode")
      .find({
        corpId,
        cateId: { $in: cateIds },
        staffQrcodeType: { $ne: "personal" },
      })
      .limit(10000)
      .project({ configId: 1, qrCodeName: 1, cateId: 1 })
      .toArray();

    if (qrcodes.length === 0) return { success: true, data: [], qrcodes: [] };

    const configIds = qrcodes.map((item) => item.configId);
    const query = { configId: { $in: configIds } };
    const dates = [];
    if (startTime && dayjs(startTime).isValid())
      dates.push({ $gte: dayjs(startTime).startOf("day").valueOf() });
    if (endTime && dayjs(endTime).isValid())
      dates.push({ $lte: dayjs(endTime).endOf("day").valueOf() });
    if (dates.length > 0) query.createTime = { $and: dates };

    const data = await db
      .collection("qrcode-statistics")
      .aggregate([
        { $match: query },
        { $group: { _id: "$configId", count: { $sum: 1 } } },
      ])
      .toArray();

    return { success: true, data, qrcodes };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
