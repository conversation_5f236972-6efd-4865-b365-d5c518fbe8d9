const envFile = `.env.${process.env.NODE_ENV || "development"}`;
require("dotenv").config({ path: envFile });

const express = require("express");
const cors = require("cors");
const timeout = require("connect-timeout");
const mongodb = require("./mongodb");
const upload = require("./upload");
const bodyParser = require("body-parser");
const corsUtils = require("./utils/cors");
const logger = require("./utils/logger");
const {
  safeSend,
  sendSuccess,
  sendError,
  asyncHandler,
} = require("./utils/response");
const {
  serverConfig,
  optimizeServer,
  setupProcessHandlers,
} = require("./utils/server-config");
const {
  monitoringMiddleware,
  errorMonitoringMiddleware,
  monitor,
} = require("./utils/performance-monitor");
const { checkDatabaseHealth, safeGetDatabase } = require("./utils/db-health");
const member = require("./member");
const todo = require("./toDoEvents");
const groupmsg = require("./groupmsg");
const corp = require("./corp");
const knowledgeBase = require("./knowledgeBase");
const survery = require("./survery");
const weCom = require("./weCom");
const system = require("./system");
const sessionArchive = require("./sessionArchive");
const customerHisSync = require("./customerHisSync");
const hlw = require("./hlw");
const trigger = require("./trigger");
const consultTrigger = require("./hlw/consult-order/trigger");
const callBack = require("./callBack");
const alipayApi = require("./alipayApi");
const path = require("path");
const { useUploadFileParse } = require("./upload-file-parse");
const app = express();
const PORT = process.env.CONFIG_NODE_PORT;
const { getDatabase, connectToMongoDB } = require("./mongodb");

// 设置进程异常处理
setupProcessHandlers();

// 设置CORS中间件，允许所有来源的请求
app.use(cors(corsUtils.cors));

// 添加性能监控中间件
app.use(monitoringMiddleware);

// 启动定期性能报告
monitor.startPeriodicReport(300000); // 每5分钟报告一次

app.use(timeout(serverConfig.timeout)); // 使用配置的超时时间

// 添加超时处理中间件 - 必须在所有路由之前
app.use((req, res, next) => {
  // 如果请求没有超时，继续处理
  if (!req.timedout) {
    next();
  } else {
    // 如果已经超时，直接返回超时错误
    sendError(res, "请求超时", "Request timeout", 408);
  }
});

// 设置请求头中间件，允许自定义请求头（如果需要的话）
app.use((req, res, next) => {
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Custom-Header"
  );
  next();
});

// 解析JSON格式的请求体 - 使用配置的大小限制
app.use(bodyParser.json({ limit: serverConfig.bodyLimit }));
// 解析urlencoded格式的请求体
app.use(
  bodyParser.urlencoded({ extended: true, limit: serverConfig.bodyLimit })
);

app.use(bodyParser.text({ type: "*/xml", limit: serverConfig.bodyLimit }));

// GET请求处理器
app.get("/", (req, res) => {
  sendSuccess(res, null, "GET request received");
});

// 连接数据库
connectToMongoDB().catch((err) => {
  logger.error("数据库连接失败，服务器将无法正常工作:", err);
  // 不退出进程，让错误处理中间件处理后续请求
});

app.get(
  "/zyt/stats",
  asyncHandler(async (req, res) => {
    try {
      const db = await getDatabase("Internet-hospital");
      const item = await hlw(
        { ...req.query, type: "hlwStats", statsType: req.query.type },
        db
      );
      safeSend(res, item);
    } catch (error) {
      logger.error("获取统计信息失败:", error);
      sendError(res, "数据库连接失败", error.message, 500);
    }
  })
);

app.get(
  "/zyt/2excel",
  asyncHandler(async (req, res) => {
    try {
      const requestData = req.query;
      logger.info(`接口请求:${JSON.stringify(requestData)}`);

      const db = await getDatabase("Internet-hospital");
      const message = await hlw(
        { ...req.query, type: "downloadXlsx", downloadType: req.query.type },
        db,
        res
      );

      if (message) {
        logger.info(`接口输出:${JSON.stringify(message)}`);
        safeSend(res, message);
      }
    } catch (error) {
      logger.error("Excel下载失败:", error);
      sendError(res, "数据库连接失败", error.message, 500);
    }
  })
);

// POST请求处理器
app.post(
  "/getYoucanData/member",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    logger.info(requestData);
    try {
      const db = await getDatabase("admin");
      let item = await member.main(requestData, db);
      logger.info(`接口请求:${JSON.stringify(requestData)}`);
      logger.info(`接口输出:${JSON.stringify(item)}`);
      safeSend(res, item);
    } catch (error) {
      logger.error("会员数据查询失败:", error);
      sendError(res, "数据库连接失败", error.message, 500);
    }
  })
);

app.post(
  "/getYoucanData/todo",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("admin");
    let item = await todo.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/groupmsg",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("admin");
    let item = await groupmsg.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/corp",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("corp");
    const item = await corp.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/knowledgeBase",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("corp");
    const item = await knowledgeBase.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/survery",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("corp");
    const item = await survery.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/system",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("corp");
    const item = await system.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/sessionArchive",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("corp");
    const item = await sessionArchive.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/weCom",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const item = await weCom.main(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/customerHisSync",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const item = await customerHisSync.main(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

// 添加静态HTML文件访问路径
app.use("/", express.static(path.join(__dirname, "./static")));

app.post(
  "/getAlipayData",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const item = await alipayApi.main(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/getYoucanData/hlw",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("Internet-hospital");
    const item = await hlw(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item);
  })
);

app.post(
  "/IMCallBack",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    const db = await getDatabase("Internet-hospital");
    await hlw(
      {
        type: "addChatMsg",
        params: requestData,
      },
      db
    );
    sendSuccess(res, null, "请求成功");
  })
);

app.get(
  "/callback/data",
  asyncHandler(async (req, res) => {
    const requestData = req.query;
    console.log(requestData);
    const item = await callBack.httpGet(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item, false);
  })
);

app.get(
  "/callback/command",
  asyncHandler(async (req, res) => {
    const requestData = req.query;
    const item = await callBack.httpGet(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, item, false);
  })
);

app.post(
  "/callback/command",
  asyncHandler(async (req, res) => {
    const requestData = req.body;
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    const item = await callBack.httpPost(requestData);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    safeSend(res, "success", false);
  })
);

app.use("/uploads", express.static(path.join(__dirname, "../uploads")));

app.use("/", express.static(path.join(__dirname, "./static")));

// 性能监控API端点
app.get(
  "/monitor/stats",
  asyncHandler(async (req, res) => {
    const stats = monitor.getStats();
    sendSuccess(res, stats, "性能统计获取成功");
  })
);

app.post(
  "/monitor/reset",
  asyncHandler(async (req, res) => {
    monitor.reset();
    sendSuccess(res, null, "性能统计已重置");
  })
);

// 数据库健康检查端点
app.get(
  "/monitor/db-health",
  asyncHandler(async (req, res) => {
    const isHealthy = await checkDatabaseHealth();
    if (isHealthy) {
      sendSuccess(res, { status: "healthy" }, "数据库连接正常");
    } else {
      sendError(res, "数据库连接异常", null, 503);
    }
  })
);

// 文件上传接口
upload.uploadFile(app);

// 错误监控中间件
app.use(errorMonitoringMiddleware);

// 全局错误处理中间件
app.use((err, req, res, next) => {
  logger.error("全局错误:", err);

  // 记录错误到监控系统
  monitor.recordError(err, req);

  if (!res.headersSent) {
    sendError(
      res,
      "服务器内部错误",
      process.env.NODE_ENV === "development"
        ? err.message
        : "Internal Server Error",
      500
    );
  }
});
useUploadFileParse(app);
// 启动服务器
const server = app.listen(PORT, "0.0.0.0", () => {
  logger.info(`Server is running on http://localhost:${PORT}`);
});

// 应用服务器优化设置
optimizeServer(server);

console.log(process.env.CONFIG_NODE_ENV);

// 定时任务
// trigger.initSchedule();

consultTrigger({
  type: "recoverDelayedTasks",
});

// 启动监管平台数据推送服务
console.log(`当前环境: NODE_ENV=${process.env.NODE_ENV}`);
if (process.env.NODE_ENV === 'pro' || process.env.NODE_ENV === 'zytDev' || process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
  const { spawn } = require('child_process');
  const path = require('path');

  const scheduleTime = process.env.REGULATORY_SCHEDULE_TIME || '23:00';
  const autoPushScript = path.join(__dirname, 'zy-regulatory', 'auto-push.js');

  console.log(`🚀 启动监管平台定时推送服务 - 每日 ${scheduleTime}`);

  const regulatoryProcess = spawn('node', [autoPushScript, 'schedule', scheduleTime], {
    stdio: ['ignore', 'pipe', 'pipe'],
    cwd: __dirname
  });

  // 监听输出
  regulatoryProcess.stdout.on('data', (data) => {
    console.log(`[监管推送] ${data.toString().trim()}`);
  });

  regulatoryProcess.stderr.on('data', (data) => {
    console.error(`[监管推送] ${data.toString().trim()}`);
  });

  regulatoryProcess.on('exit', (code) => {
    if (code !== 0) {
      console.error(`[监管推送] 进程退出，退出码: ${code}`);
    }
  });

  // 优雅停止处理
  process.on('SIGINT', () => {
    regulatoryProcess.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    regulatoryProcess.kill('SIGTERM');
  });
}
