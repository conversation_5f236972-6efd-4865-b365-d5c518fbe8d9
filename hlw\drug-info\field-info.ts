import { ObjectId } from "mongodb";

interface Drug {
  _id: ObjectId; // 数据库中的唯一标识符
  pinyin_code: string; // 拼音码
  name: string; // 药品名称
  product_id?: number; // 产品ID
  product_id_str?: string; // 产品ID的字符串形式
  specification: string; // 药品规格
  package_amount: number; // 包装数量
  manufacturer: string; // 生产厂家
  unit: string; // 单位
  category: string; // 药品类别
  barcode: string; // 条形码
  insurance_code: string; // 医保编码
  dosage: number;// 单次剂量
  dosage_unit: string; // 单次剂量单位
  frequency: string; // 频次
  freq: string; // 频次
  administration_method: string; // 给药方式
  days: number; // 用药天数
  recommended_quantity: number; // 推荐数量
  effect?: string; // 药品功效
  dosage_form?: string; // 剂型
  data_source?: string; // 数据来源
  createTime?: Date; // 创建时间
  onSale?: boolean; // 是否上架（是否在售） 
}