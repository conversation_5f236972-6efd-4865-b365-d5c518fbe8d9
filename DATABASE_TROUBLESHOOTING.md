# 数据库连接故障排除指南

## 错误类型：TypeError: Cannot read properties of undefined (reading 'collection')

这个错误通常表示数据库对象是 `undefined`，即数据库连接失败或未正确初始化。

## 快速诊断

### 1. 检查环境变量
```bash
npm run check-env
```

### 2. 测试数据库连接
```bash
npm run test-db
```

### 3. 检查服务器状态
```bash
# 检查数据库健康状态
curl http://localhost:3000/monitor/db-health

# 检查服务器性能
curl http://localhost:3000/monitor/stats
```

## 常见原因及解决方案

### 1. 环境变量未设置或错误

**症状：**
- 启动时看到 "MongoDB连接参数缺失" 错误
- 环境变量为空或undefined

**解决方案：**
1. 检查环境文件 `.env.development`、`.env.pro` 或 `.env.zytDev` 是否存在
2. 确保包含以下变量：
   ```
   CONFIG_DB_USERNAME=your_username
   CONFIG_DB_PASSWORD=your_password
   CONFIG_DB_HOST=your_host
   CONFIG_DB_PORT=27017
   CONFIG_NODE_PORT=3000
   ```

### 2. MongoDB服务器未运行

**症状：**
- 连接超时错误
- "connection refused" 错误

**解决方案：**
1. 检查MongoDB服务器是否运行
2. 验证主机地址和端口是否正确
3. 检查网络连接

### 3. 数据库权限问题

**症状：**
- "authentication failed" 错误
- "not authorized" 错误

**解决方案：**
1. 验证用户名和密码是否正确
2. 确认数据库用户有足够的权限
3. 检查数据库名称是否正确

### 4. 连接池问题

**症状：**
- 间歇性连接失败
- 高并发时出现错误

**解决方案：**
1. 调整连接池配置（在 `mongodb.js` 中）
2. 增加连接超时时间
3. 监控连接池状态

## 应急修复步骤

### 1. 立即检查
```bash
# 1. 检查环境配置
npm run check-env

# 2. 测试数据库连接
npm run test-db

# 3. 查看应用日志
tail -f logs/application.log
```

### 2. 重启服务
```bash
# 重启应用
pm2 restart bundle_pro

# 或者如果是直接运行
pkill -f "node.*bundle_pro"
npm run pro
```

### 3. 数据库重连
```bash
# 检查MongoDB服务状态
systemctl status mongod

# 重启MongoDB（如果需要）
systemctl restart mongod
```

## 监控和预防

### 1. 设置监控

定期检查以下端点：
- `GET /monitor/db-health` - 数据库健康状态
- `GET /monitor/stats` - 应用性能统计

### 2. 日志监控

关键日志信息：
- "MongoDB 连接成功" - 连接建立
- "MongoDB连接失败" - 连接失败
- "数据库连接已断开" - 连接中断

### 3. 添加告警

建议在以下情况下发送告警：
- 数据库连接失败
- 错误率超过5%
- 响应时间过长

## 代码改进

### 1. 错误处理增强

所有数据库操作都应该包含错误处理：

```javascript
try {
  const db = await getDatabase("your-db-name");
  const result = await db.collection("your-collection").find({}).toArray();
  return result;
} catch (error) {
  logger.error("数据库操作失败:", error);
  throw new Error(`数据库连接错误: ${error.message}`);
}
```

### 2. 连接状态检查

在执行数据库操作前检查连接：

```javascript
const { isConnected } = require('./mongodb');

if (!isConnected()) {
  throw new Error("数据库连接已断开");
}
```

### 3. 重试机制

对于临时连接问题，实现重试逻辑：

```javascript
async function retryDatabaseOperation(operation, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 环境变量检查结果 (`npm run check-env`)
2. 数据库连接测试结果 (`npm run test-db`)
3. 应用日志文件
4. 错误发生的具体时间和频率
5. 当前的系统负载情况

## 更新历史

- 2024-01-XX: 添加数据库连接错误处理
- 2024-01-XX: 增强监控和健康检查功能
- 2024-01-XX: 完善故障排除流程 