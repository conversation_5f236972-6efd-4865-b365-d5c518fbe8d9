#!/usr/bin/env node

/**
 * 生产环境测试脚本
 * 验证生产构建版本是否包含数据库连接修复
 */

const { spawn } = require('child_process');
const path = require('path');

async function testProductionEnvironment() {
  console.log('🏭 开始生产环境测试...');
  console.log('验证 bundle_pro.js 中的数据库连接错误是否已修复');
  
  // 测试生产环境配置
  process.env.NODE_ENV = 'pro';
  
  return new Promise((resolve, reject) => {
    console.log('\n1️⃣ 启动生产版本应用...');
    
    // 启动生产版本
    const productionApp = spawn('node', ['dist/bundle_pro.js'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, NODE_ENV: 'pro' }
    });
    
    let output = '';
    let errorOutput = '';
    let hasStarted = false;
    let hasError = false;
    
    // 收集输出
    productionApp.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log('📤 应用输出:', text.trim());
      
      // 检查启动成功标志
      if (text.includes('Server is running') || text.includes('MongoDB 连接成功')) {
        hasStarted = true;
      }
    });
    
    // 收集错误输出
    productionApp.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      console.log('📥 错误输出:', text.trim());
      
      // 检查是否有原始错误
      if (text.includes("Cannot read properties of undefined (reading 'collection')")) {
        console.error('🚨 原始错误仍然存在于生产版本中！');
        hasError = true;
      }
      
      // 检查其他数据库相关错误
      if (text.includes('MongoDB连接失败') || text.includes('数据库连接参数缺失')) {
        console.error('⚠️ 数据库连接问题:', text.trim());
      }
    });
    
    // 进程退出处理
    productionApp.on('close', (code) => {
      console.log(`\n📊 应用进程退出，代码: ${code}`);
      
      // 分析结果
      if (hasError) {
        console.error('❌ 测试失败: 原始错误仍然存在');
        console.error('建议: 检查生产构建是否包含最新修复');
        reject(new Error('生产环境仍有 collection 错误'));
      } else if (hasStarted) {
        console.log('✅ 测试成功: 应用正常启动，未发现原始错误');
        resolve({ success: true, output, errorOutput });
      } else {
        console.log('⚠️ 应用启动过程异常，但未发现原始错误');
        resolve({ success: false, output, errorOutput, reason: '启动异常' });
      }
    });
    
    // 进程错误处理
    productionApp.on('error', (error) => {
      console.error('❌ 进程启动失败:', error.message);
      reject(error);
    });
    
    // 5秒后主动结束测试
    setTimeout(() => {
      console.log('\n⏱️ 测试时间到，结束应用进程...');
      productionApp.kill('SIGTERM');
      
      // 强制结束
      setTimeout(() => {
        if (!productionApp.killed) {
          productionApp.kill('SIGKILL');
        }
      }, 2000);
    }, 5000);
  });
}

async function main() {
  try {
    const result = await testProductionEnvironment();
    
    console.log('\n📋 测试结果总结:');
    
    if (result.success) {
      console.log('✅ 生产环境测试通过');
      console.log('✅ 原始错误 "Cannot read properties of undefined (reading \'collection\')" 已修复');
      console.log('✅ 应用能够正常启动');
    } else {
      console.log('⚠️ 生产环境测试部分通过');
      console.log('⚠️ 原始错误未出现，但启动过程有异常');
      console.log('原因:', result.reason);
    }
    
    // 输出关键日志信息
    if (result.output) {
      console.log('\n📝 关键输出信息:');
      const keyLines = result.output.split('\n').filter(line => 
        line.includes('MongoDB') || 
        line.includes('连接') || 
        line.includes('Server') ||
        line.includes('Error') ||
        line.includes('error')
      );
      keyLines.forEach(line => console.log('  ', line.trim()));
    }
    
  } catch (error) {
    console.error('\n❌ 生产环境测试失败:', error.message);
    
    if (error.message.includes('collection')) {
      console.error('🚨 确认：原始错误仍然存在于生产环境中');
      console.error('🔧 建议解决方案:');
      console.error('1. 重新构建生产版本: npm run build:pro');
      console.error('2. 检查生产环境配置文件');
      console.error('3. 验证环境变量设置');
      console.error('4. 重启生产服务');
    }
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { testProductionEnvironment }; 