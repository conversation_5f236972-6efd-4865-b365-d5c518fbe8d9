
function isMobile(val) {
  // 正则表达式验证手机号
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(val);
}

function verifyString(str, label, min = 0, max = 100) {
  if (typeof str === 'number') {
    str = str.toString()
  }
  if (str === undefined || str === null) {
    str = ''
  }
  if (typeof str === 'string' && str.trim().length >= min && str.trim().length < max) {
    return [true, str.trim()];
  }
  if (typeof str !== 'string' && min === 0) {
    return [true, ''];
  }
  if (typeof str !== 'string') {
    return [false, `${label}格式不正确`];
  }
  if (str.trim().length === 0 && min > 0) {
    return [false, `${label}不能为空`];
  }
  if (min > 0 && str.trim().length < min) {
    return [false, `${label}不能少于${min}个字符`];
  }
  return [false, `${label}不能超过${max}个字符`]
}

function verifyNumber(value, label, len = 0, min, max, minEq = false, maxEq = false) {
  // 1. 检查 value 是否为字符串或数字类型
  if (typeof value !== 'string' && typeof value !== 'number') {
    return [false, `${label}无效`];
  }

  // 2. 如果是字符串类型，尝试转为数字
  if (typeof value === 'string') {
    // 去除前后空格
    value = value.trim();
    // 空字符串或非数字字符串直接返回 false
    if (value === '' || isNaN(value)) {
      return [false, `${label}无效`];
    }
    value = Number(value);
  }

  // 3. 检查是否为有效数字（排除 NaN 和 Infinity）
  if (typeof value !== 'number' || !isFinite(value)) {
    return [false, `${label}无效`];
  }

  // 4. 检查小数位数是否 <= len
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > len) {
    return len === 0 ? [false, `${label}只能输入整数`] : [false, `${label}最多输入${len}位小数`];
  }
  if (typeof min === 'number' && minEq && value < min) {
    return [false, `${label}不能小于${min}`];
  }
  if (typeof min === 'number' && !minEq && value <= min) {
    return [false, `${label}应该大于${min}`];
  }
  if (typeof max === 'number' && maxEq && value > max) {
    return [false, `${label}不能大于${max}`];
  }
  if (typeof max === 'number' && !maxEq && value >= max) {
    return [false, `${label}应该小于${max}`];
  }

  return [true, value];
}

function verifyListItem(val, list, required, label, defaultValue = '') {
  if (!val && val !== 0 && !required) {
    return [true, defaultValue]
  }
  if (list.includes(val)) {
    return [true, val]
  }
  return [false, `${label}无效`]
}

module.exports = {
  verifyString,
  verifyNumber,
  verifyListItem,
  isMobile
}