#!/usr/bin/env node

/**
 * 数据库连接测试脚本
 * 用于测试MongoDB连接是否正常工作
 */

// 加载环境变量
const envFile = `.env.${process.env.NODE_ENV || "development"}`;
require("dotenv").config({ path: envFile });

const { connectToMongoDB, getDatabase, closeMongoDB } = require('./mongodb');

async function testDatabaseConnection() {
  console.log('🔄 开始测试数据库连接...');
  
  try {
    // 1. 测试连接建立
    console.log('1️⃣ 连接到MongoDB...');
    await connectToMongoDB();
    console.log('✅ MongoDB连接成功');
    
    // 2. 测试各个数据库
    const databases = ['admin', 'corp', 'Internet-hospital'];
    
    for (const dbName of databases) {
      console.log(`2️⃣ 测试数据库: ${dbName}`);
      try {
        const db = await getDatabase(dbName);
        
        // 测试ping
        await db.admin().ping();
        console.log(`✅ 数据库 ${dbName} 连接正常`);
        
        // 测试collection操作
        const testCollection = db.collection('test-connection');
        const testDoc = { _id: 'test', timestamp: new Date() };
        
        // 插入测试文档
        await testCollection.insertOne(testDoc);
        console.log(`✅ 数据库 ${dbName} 写入测试成功`);
        
        // 读取测试文档
        const result = await testCollection.findOne({ _id: 'test' });
        if (result) {
          console.log(`✅ 数据库 ${dbName} 读取测试成功`);
        }
        
        // 删除测试文档
        await testCollection.deleteOne({ _id: 'test' });
        console.log(`✅ 数据库 ${dbName} 删除测试成功`);
        
      } catch (error) {
        console.error(`❌ 数据库 ${dbName} 测试失败:`, error.message);
        throw error;
      }
    }
    
    console.log('\n🎉 所有数据库连接测试通过！');
    
  } catch (error) {
    console.error('\n❌ 数据库连接测试失败:', error.message);
    console.error('详细错误信息:', error);
    
    // 提供故障排除建议
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查环境变量是否正确设置 (运行: node check-env.js)');
    console.log('2. 确认MongoDB服务器是否运行');
    console.log('3. 检查网络连接');
    console.log('4. 验证数据库用户权限');
    
    process.exit(1);
  } finally {
    // 清理连接
    await closeMongoDB();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testDatabaseConnection();
}

module.exports = { testDatabaseConnection }; 