const validator = require('../../utils/validator.js');

const rules = [
  {
    key: 'doctorName',
    validate: val => validator.verifyString(val, '姓名', 1, 30)
  },
  {
    key: 'sex',
    validate: val => ['男', '女'].includes(val) ? [true, val] : [true, '']
  },
  {
    key: 'mobile',
    validate: val => {
      if (typeof val === 'string' && val.trim() !== '') {
        const isMobile = validator.isMobile(val);
        return [isMobile, isMobile ? val.trim() : '请输入正确的手机号'];
      }
      return [true, '']
    }
  },
  {
    key: 'deptCode',
    validate: val => validator.verifyString(val, '科室编码', 0, 30)
  },
  {
    key: 'deptName',
    validate: val => validator.verifyString(val, '科室名称', 0, 30)
  },
  {
    key: 'job',
    validate: val => {
      if (val === 'doctor' || val === 'pharmacist') {
        return [true, val];
      }
      if (val) {
        return [false, '无效的岗位'];
      }
      return [true, ''];
    }
  },
  {
    key: 'doctor<PERSON>o',
    validate: val => {
      if (/\W/.test(val)) {
        return [false, '院内工号只能包含字母、数字和下划线'];
      }
      return validator.verifyString(val, '院内工号', 1, 50)
    }
  },
  {
    key: 'title',
    validate: val => validator.verifyString(val, '职称', 0, 30)
  },
  {
    key: 'intro',
    validate: val => validator.verifyString(val, '个人介绍', 0, 300)
  },
  {
    key: 'goodAt',
    validate: val => validator.verifyString(val, '擅长领域', 0, 300)
  },
  {
    key: 'clinicTime',
    validate: val => validator.verifyString(val, '门诊时间', 0, 100)
  }
];


function getPeopleData(params, testAll = true) {
  if (Object.prototype.toString.call(params) !== '[object Object]') {
    return { success: false, message: '参数格式不正确' };
  }
  const data = {};
  for (const rule of rules) {
    const { key, validate } = rule;
    const testField = testAll ? true : (key in params);
    if (!testField) continue;
    const value = params[key];
    const [valid, message] = validate(value);
    if (!valid) {
      console.log(`验证失败: ${key} - ${message}`);
      return { success: false, message };
    }
    data[key] = message;
  }
  return { data, success: true, message: '验证通过' };
}

module.exports = { getPeopleData }