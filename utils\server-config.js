const logger = require("./logger");

/**
 * 服务器优化配置
 */
const serverConfig = {
  // 超时设置
  timeout: 30000, // 30秒
  
  // 请求体大小限制
  bodyLimit: '10mb',
  
  // 并发连接数限制
  maxConnections: 1000,
  
  // Keep-alive 超时
  keepAliveTimeout: 5000,
  
  // Headers 超时
  headersTimeout: 60000,
};

/**
 * 设置服务器优化参数
 * @param {Object} server - Express服务器实例
 */
function optimizeServer(server) {
  if (server) {
    // 设置keep-alive超时
    server.keepAliveTimeout = serverConfig.keepAliveTimeout;
    
    // 设置headers超时
    server.headersTimeout = serverConfig.headersTimeout;
    
    // 设置最大连接数
    server.maxConnections = serverConfig.maxConnections;
    
    // 监听服务器错误
    server.on('error', (error) => {
      logger.error('服务器错误:', error);
    });
    
    // 监听连接错误
    server.on('clientError', (err, socket) => {
      logger.error('客户端连接错误:', err);
      if (!socket.destroyed) {
        socket.end('HTTP/1.1 400 Bad Request\r\n\r\n');
      }
    });
    
    // 优雅关闭处理
    process.on('SIGTERM', () => {
      logger.info('收到SIGTERM信号，开始优雅关闭服务器...');
      server.close(() => {
        logger.info('服务器已关闭');
        process.exit(0);
      });
    });
    
    process.on('SIGINT', () => {
      logger.info('收到SIGINT信号，开始优雅关闭服务器...');  
      server.close(() => {
        logger.info('服务器已关闭');
        process.exit(0);
      });
    });
  }
}

/**
 * 添加进程异常处理
 */
function setupProcessHandlers() {
  // 未捕获的异常
  process.on('uncaughtException', (error) => {
    logger.error('未捕获的异常:', error);
    // 优雅退出
    process.exit(1);
  });
  
  // 未处理的Promise拒绝
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('未处理的Promise拒绝:', reason);
    logger.error('Promise:', promise);
  });
}

module.exports = {
  serverConfig,
  optimizeServer,
  setupProcessHandlers
}; 