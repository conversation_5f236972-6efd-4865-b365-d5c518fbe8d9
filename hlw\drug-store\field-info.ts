import { ObjectId } from "mongodb";

enum StoreType {
  直营 = "直营",
  加盟 = "加盟",
  其他 = "其他"
}

enum StoreStatus {
  "停用",
  "正常营业",
}

interface Store {
  _id: ObjectId; // 数据库中的唯一标识符
  name: string; // 药品名称
  store_id: string; // 药店ID
  type: StoreType; // 药店类型
  login_name: string; // 关联登录账号名称
  login_no: string; // 关联登录账号
  area: string;// 所属区域
  charge_id:string; // 互联网诊查费(收费编码)
  region: string[]; // 省市区信息
  status: StoreStatus; // 药店状态
  statusCode: number; // 药店状态码 停用 0 正常营业 1
  introduce:string; // 药店简介 (300字以内)
  imgs:string[]; // 药店图片
  address: string; // 详情地址
  longitude:string; // 经度
  latitude: string; // 纬度
  openingHours:string; // 营业时间
  createTime?: Date; // 创建时间
}