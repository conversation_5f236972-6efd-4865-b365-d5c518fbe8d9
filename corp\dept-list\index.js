let db = null;
const common = require("../../common");
const api = require("../../api");

exports.main = async (context, mongodb) => {
  db = mongodb;
  switch (context.type) {
    case "addDept":
      return await addDept(context);
    case "updateDept":
      return await updateDept(context);
    case "deleteDept":
      return await deleteDept(context);
    case "getDeptList":
      return await getDeptList(context);
    case "sortDeptList":
      return await sortDeptList(context);
    case "ifCateExist":
      return await ifCateExist(context);
    case "addDeptUserId":
      return await addDeptUserId(context);
    case "deleteDeptUserId":
      return await deleteDeptUserId(context);
    case "getUsersByDept":
      return await getUsersByDept(context);
    case "addDeptStaff":
      return await addDeptStaff(context);
    case "removeDeptStaff":
      return await removeDeptStaff(context);
    case "getDeptStaff":
      return await getDeptStaff(context);
    case "updateStaffDept":
      return await updateStaffDept(context);
  }
};

async function addDept(context) {
  let { corpId, parentId, params } = context;
  const { deptName, deptId } = params;
  if (typeof corpId !== 'string' || corpId.trim() === '') {
    return { success: false, message: "无效的机构id" }
  }
  if (typeof deptName !== "string" || deptName.trim() === "") {
    return { success: false, message: "科室名称无效" };
  }
  if (typeof deptId !== "string" || deptId.trim() === "") {
    return { success: false, message: "科室ID无效" };
  }
  if (/\W/.test(deptId)) {
    return { success: false, message: "科室ID只能由数字字母组成" };
  }
  if (deptName.trim().length > 10)
    return { success: false, message: "科室名称不能超过10个字" };
  try {
    const data = {
      corpId: corpId.trim(),
      deptName: deptName.trim(),
      deptId: deptId.trim(),
      deptDesc: typeof params.deptDesc === "string" ? params.deptDesc.trim() : "",
    }
    const total = await db.collection("dept-list").countDocuments({ corpId, deptId: deptId.trim() });

    if (total > 0) {
      return { success: false, message: "科室ID已存在" };
    }
    if (parentId) {
      const parent = await db
        .collection("dept-list")
        .findOne({ _id: parentId, corpId });
      if (!parent) return { success: false, message: "父级科室不存在" };
      if (![1, 2].includes(parent.level))
        return { success: false, message: "父级科室层级错误" };
      const res = await db.collection("dept-list").insertOne({
        _id: common.generateRandomString(24),
        corpId,
        level: parent.level + 1,
        parentId,
        createTime: Date.now(),
        ...data,
      });
      return { success: true, data: res, message: "添加科室成功" };
    }
    const res = await db.collection("dept-list").insertOne({
      _id: common.generateRandomString(24),
      corpId,
      level: 1,
      createTime: Date.now(),
      ...data,
    });
    return { success: true, data: res, message: "添加科室成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateDept(context) {
  let { corpId, id: _id, params } = context;
  const { deptName, deptId } = params;
  if (typeof corpId !== 'string' || corpId.trim() === '') return { success: false, message: "机构id不能为空" };
  if (typeof _id !== 'string' || _id.trim() === '') return { success: false, message: "科室id不能为空" };
  if (typeof deptName !== "string" || deptName.trim() === "") {
    return { success: false, message: "科室名称无效" };
  }
  if (deptName.trim().length > 10) {
    return { success: false, message: "科室名称不能超过10个字" };
  }
  if (typeof deptId !== "string" || deptId.trim() === "") {
    return { success: false, message: "科室ID无效" };
  }
  if (/\W/.test(deptId)) {
    return { success: false, message: "科室ID只能由数字字母组成" };
  }
  try {
    const data = {
      corpId: corpId.trim(),
      deptName: deptName.trim(),
      deptId: deptId.trim(),
      deptDesc: typeof params.deptDesc === "string" ? params.deptDesc.trim() : "",
    }
    const total = await db.collection("dept-list").countDocuments({ corpId, deptId: data.deptId, _id: { $ne: _id } });
    if (total > 0) {
      return { success: false, message: "科室ID已存在其他科室" };
    }
    const dept = await db.collection("dept-list").findOne({ _id, corpId }, { projection: { deptId: 1, deptName: 1 } });
    if (!dept) return { success: false, message: "科室不存在" };

    const res = await db
      .collection("dept-list")
      .updateOne({ _id: dept._id }, { $set: data });
    if (dept.deptId !== data.deptId || dept.deptName !== data.deptName) {
      console.log('需要更新对应信息')
      await updateHlwPeopleDept({ corpId, deptCodes: [dept.deptId], newDeptCode: data.deptId, newDeptName: data.deptName });
    }
    return { success: true, data: res, message: "更新科室成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function addDeptUserId(context) {
  let { corpId, deptId, userIds } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!deptId) return { success: false, message: "科室id不能为空" };
  if (!Array.isArray(userIds) || userIds.length === 0)
    return { success: false, message: "用户id不能为空" };
  try {
    await db
      .collection("dept-list")
      .updateOne(
        { corpId, _id: deptId },
        { $addToSet: { userIds: { $each: userIds } } }
      );
    return { success: true, message: "添加用户成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteDeptUserId(context) {
  let { corpId, deptId, userId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!deptId) return { success: false, message: "科室id不能为空" };
  if (!userId) return { success: false, message: "用户id不能为空" };
  try {
    await db
      .collection("dept-list")
      .updateOne({ corpId, _id: deptId }, { $pull: { userIds: userId } });
    return { success: true, message: "删除用户成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getUsersByDept(context) {
  let { corpId, deptId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!deptId) return { success: false, message: "科室id不能为空" };
  try {
    const data = await db
      .collection("dept-list")
      .aggregate([
        { $match: { corpId, _id: deptId } },
        {
          $lookup: {
            from: "corp-member",
            localField: "userIds",
            foreignField: "userid",
            as: "users",
          },
        },
        { $project: { _id: 0, users: 1 } },
      ])
      .toArray();
    if (data.length === 0) {
      return { success: false, message: "未找到相关用户" };
    }
    return { success: true, list: data, message: "查询用户成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteDept(context) {
  let { corpId, id: _id } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "科室id不能为空" };
  try {
    const cate = await db.collection("dept-list").findOne({ _id: _id, corpId }, { projection: { level: 1, _id: 1, deptId: 1 } });
    if (!cate) return { success: false, message: "科室不存在" };
    const ids = [_id];
    const deptIds = [cate.deptId];
    if (cate.level < 3) {
      const children = await db
        .collection("dept-list")
        .find({ corpId, parentId: _id }, { projection: { _id: 1, deptId: 1 } })
        .toArray();
      const childrenIds = children.map((item) => item._id);
      deptIds.push(...children.map((item) => item.deptId));
      if (cate.level === 1 && childrenIds.length) {
        const grandchildren = await db
          .collection("dept-list")
          .find({ corpId, parentId: { $in: childrenIds } }, { projection: { _id: 1, deptId: 1 } })
          .toArray();
        ids.push(...grandchildren.map((item) => item._id));
        deptIds.push(...grandchildren.map((item) => item.deptId));
      }
      ids.push(...childrenIds);
    }
    const { success } = await updateHlwPeopleDept({ corpId, deptCodes: deptIds, newDeptCode: '', newDeptName: '' })
    if (!success) {
      return { success: false, message: "删除互联网医院科室失败" };
    }
    const res = await db
      .collection("dept-list")
      .deleteMany({ corpId, _id: { $in: ids } });
    return { success: true, data: res, message: "删除科室成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getDeptList(context) {
  let { corpId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const list = await db
      .collection("dept-list")
      .find({ corpId, init: { $exists: false } })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    return { success: true, list, message: "获取科室成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function sortDeptList(context) {
  const { corpId, sortData } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const arr = Array.isArray(sortData)
    ? sortData.filter(
      (i) =>
        i._id && typeof i.sort === "number" && i.sort % 1 === 0 && i.sort >= 0
    )
    : [];
  if (arr.length === 0) return { success: false, message: "参数错误" };
  try {
    const bulkOps = arr.map((item) => ({
      updateOne: {
        filter: { corpId, _id: item._id },
        update: { $set: { sort: item.sort } },
      },
    }));
    const res = await db.collection("dept-list").bulkWrite(bulkOps);
    return { success: true, message: "操作成功", res };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function ifCateExist(context) {
  const { corpId, cateId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const cate = await db
      .collection("dept-list")
      .findOne({ corpId, _id: cateId });
    return cate ? { success: true } : { success: false, message: "科室不存在" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
async function addDeptStaff(ctx) {
  const { corpId, deptId, userIds } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!deptId) return { success: false, message: "科室id不能为空" };
  if (!Array.isArray(userIds) || userIds.length === 0)
    return { success: false, message: "人员id不能为空" };

  try {
    const dept = await db
      .collection("dept-list")
      .findOne({ corpId, _id: deptId });
    if (!dept) return { success: false, message: "科室不存在" };

    const staffs = await db
      .collection("corp-member")
      .find({ corpId, userid: { $in: userIds } })
      .toArray();

    const bulkOps = staffs.map((staff) => {
      if (Array.isArray(staff.deptIds)) {
        return {
          updateOne: {
            filter: { corpId, _id: staff._id },
            update: { $push: { deptIds: deptId } },
          },
        };
      } else {
        return {
          updateOne: {
            filter: { corpId, _id: staff._id },
            update: { $set: { deptIds: [deptId] } },
          },
        };
      }
    });

    const res = await db.collection("corp-member").bulkWrite(bulkOps);
    return { success: true, message: "操作成功", res };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function removeDeptStaff(ctx) {
  const { corpId, deptId, userId } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!deptId) return { success: false, message: "科室id不能为空" };
  if (!userId) return { success: false, message: "人员id不能为空" };
  try {
    const staff = await db
      .collection("corp-member")
      .findOne({ corpId, deptIds: deptId, userid: userId });
    if (staff) {
      const res = await db
        .collection("corp-member")
        .updateOne(
          { _id: staff._id },
          { $pull: { deptIds: deptId } }
        );
      return { success: true, message: "操作成功", res };
    }
    return { success: false, message: "科室人员不存在" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getDeptStaff(ctx) {
  const { corpId, deptId } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!deptId) return { success: false, message: "科室id不能为空" };
  try {
    const query = { corpId, deptIds: deptId };
    const list = await db
      .collection("corp-member")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "dept-list",
            localField: "deptIds",
            foreignField: "_id",
            as: "depts",
          },
        },
        { $unwind: { path: "$depts", preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: "$_id",
            deptNames: { $addToSet: "$depts.deptName" },
            name: { $first: "$anotherName" },
            job: { $first: "$job" },
            userId: { $first: "$userid" },
            deptIds: { $first: "$deptIds" },
          },
        },
        {
          $project: {
            _id: 1,
            deptIds: 1,
            deptNames: 1,
            name: 1,
            job: 1,
            userId: 1,
          },
        },
      ])
      .limit(1000)
      .toArray();
    return { success: true, message: "查询成功", list };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateStaffDept(ctx) {
  const { corpId, deptIds, userId } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!userId) return { success: false, message: "人员id不能为空" };
  if (!Array.isArray(deptIds) || !deptIds.length)
    return { success: false, message: "科室id无效" };
  try {
    const depts = await db
      .collection("dept-list")
      .find({ corpId, _id: { $in: deptIds } })
      .toArray();
    const invalidIds = deptIds.filter(
      (id) => !depts.some((d) => d._id === id)
    );
    if (invalidIds.length)
      return { success: false, message: `科室id无效: ${invalidIds.join(",")}` };
    const staff = await db
      .collection("corp-member")
      .findOne({ corpId, userid: userId });
    if (staff) {
      const res = await db
        .collection("corp-member")
        .updateOne({ corpId, _id: staff._id }, { $set: { deptIds } });
      return { success: true, message: "操作成功", res };
    }
    return { success: false, message: "科室人员不存在" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}


async function updateHlwPeopleDept({ corpId, deptCodes, newDeptCode, newDeptName }) {
  try {
    const res = await api.getInternetHospitalApi({ type: 'updateHlwPersonDeptInfo', corpId, deptCodes, newDeptCode, newDeptName });
    return res
  } catch (e) {
    return { success: false, message: e.message }
  }
}