const weCom = require("./session-archive-auth/weCom/index");
const sessionArchiveAuth = require("./session-archive-auth/index");
const chatRecord = require("./chat-record/index");
const aiSession = require("./ai-session/index");
exports.main = async function (context, db) {
  switch (context.type) {
    case "getSessionArchiveUserList":
    case "batchStorageSession":
    case "searchChatRecord":
    case "setCorpPublicKey":
      return await weCom.main(context, db);
    case "createSessionArchiveAuth":
    case "cancelSessionArchiveAuth":
    case "getCorpSessionArchive":
      return await sessionArchiveAuth.main(context, db);
    case "getCorpMemberChatList":
    case "getCorpMemberChatRecord":
    case "getChatCount":
    case "getToDayAllChatRecord":
    case "getReplyChatRecord":
      return await chatRecord.main(context, db);
    case "getRecommendResult":
    case "getSummaryResult":
    case "getEmotionResult":
    case "createEmotionTask":
    case "createRecommendTask":
    case "createSummaryTask":
    case "getDocumentList":
      return aiSession.main(context, db);
  }
};
