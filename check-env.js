#!/usr/bin/env node

/**
 * 环境变量检查脚本
 * 用于检查MongoDB连接所需的环境变量是否正确设置
 */

const path = require('path');
const fs = require('fs');

// 获取当前环境
const nodeEnv = process.env.NODE_ENV || 'development';
const envFile = `.env.${nodeEnv}`;

console.log('🔍 检查环境配置...');
console.log(`当前环境: ${nodeEnv}`);
console.log(`环境文件: ${envFile}`);

// 检查环境文件是否存在
if (fs.existsSync(envFile)) {
  console.log('✅ 环境文件存在');
  
  // 加载环境变量
  require('dotenv').config({ path: envFile });
  
  // 读取环境文件内容
  const envContent = fs.readFileSync(envFile, 'utf8');
  console.log('\n📄 环境文件内容:');
  console.log(envContent);
} else {
  console.log('❌ 环境文件不存在');
  console.log('请确保环境文件存在:', path.resolve(envFile));
}

// 检查必需的环境变量
const requiredEnvVars = [
  'CONFIG_DB_USERNAME',
  'CONFIG_DB_PASSWORD', 
  'CONFIG_DB_HOST',
  'CONFIG_DB_PORT',
  'CONFIG_NODE_PORT'
];

console.log('\n🔧 检查必需的环境变量:');

let missingVars = [];
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // 对于密码等敏感信息，只显示部分内容
    const displayValue = varName.includes('PASSWORD') 
      ? '***' + value.slice(-3)
      : value;
    console.log(`✅ ${varName}: ${displayValue}`);
  } else {
    console.log(`❌ ${varName}: 未设置`);
    missingVars.push(varName);
  }
});

// 构造MongoDB连接字符串
const username = process.env.CONFIG_DB_USERNAME;
const password = process.env.CONFIG_DB_PASSWORD;
const host = process.env.CONFIG_DB_HOST;
const port = process.env.CONFIG_DB_PORT || '27017';

if (username && password && host) {
  const mongoUrl = `mongodb://${username}:${password.replace(/./g, '*')}@${host}:${port}/admin`;
  console.log('\n🔗 MongoDB连接字符串:');
  console.log(mongoUrl);
} else {
  console.log('\n❌ 无法构造MongoDB连接字符串，缺少必要参数');
}

// 总结
console.log('\n📊 检查结果:');
if (missingVars.length === 0) {
  console.log('✅ 所有必需的环境变量都已设置');
} else {
  console.log(`❌ 缺少 ${missingVars.length} 个必需的环境变量:`);
  missingVars.forEach(varName => {
    console.log(`   - ${varName}`);
  });
  console.log('\n💡 解决方案:');
  console.log('1. 检查环境文件是否存在');
  console.log('2. 确保环境变量拼写正确');
  console.log('3. 检查环境变量值是否正确设置');
  process.exit(1);
}

console.log('\n🚀 环境检查完成！'); 