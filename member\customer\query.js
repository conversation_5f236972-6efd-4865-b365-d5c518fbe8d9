const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getCustomersCount":
      return await getCustomersCount(content);
    case "getInHospitalCustomersCount":
      return await getInHospitalCustomersCount(content);
  }
};

// 查询报备记录人数
async function getCustomersCount(content) {
  const { corpId, dates, params, reportTeamId, reportUserIds } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let query = {
    corpId,
    ...params,
  };

  if (reportTeamId) query.reportTeamId = reportTeamId;
  if (reportUserIds && Array.isArray(reportUserIds) && reportUserIds.length > 0) {
    query.reportUserId = { $in: reportUserIds };
  }
  if (dates && dates.length > 0) {
    query.createTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }

  const total = await db.collection("member").countDocuments(query);
  return { success: true, count: total, message: "查询成功",query };
}

// 查询到院人数
async function getInHospitalCustomersCount(content) {
  const { corpId, dates, reportTeamId, reportUserIds } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let query = {
    corpId,
    addMethod: "eStoreReport",
  };

  if (reportTeamId) query.reportTeamId = reportTeamId;
  if (reportUserIds && Array.isArray(reportUserIds) && reportUserIds.length > 0) {
    query.reportUserId = { $in: reportUserIds };
  }
  if (dates && dates.length > 0) {
    query.inHospitalTimes = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  } else {
    query.inHospitalTimes = { $exists: true, $ne: [] };
  }

  const total = await db.collection("member").countDocuments(query);
  return { success: true, count: total, message: "查询成功" };
}
