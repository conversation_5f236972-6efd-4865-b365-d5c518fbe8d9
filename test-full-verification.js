#!/usr/bin/env node

/**
 * 完整验证测试脚本
 * 模拟原始的数据库操作，验证错误是否已修复
 */

// 加载环境变量
const envFile = `.env.${process.env.NODE_ENV || "development"}`;
require("dotenv").config({ path: envFile });

const { connectToMongoDB, getDatabase, closeMongoDB } = require('./mongodb');

async function testFullVerification() {
  console.log('🔬 开始完整验证测试...');
  console.log('模拟原始错误场景: TypeError: Cannot read properties of undefined (reading \'collection\')');
  
  let testsPassed = 0;
  let testsTotal = 0;
  
  try {
    // 测试1: 验证数据库连接初始化
    testsTotal++;
    console.log('\n1️⃣ 测试数据库连接初始化...');
    await connectToMongoDB();
    console.log('✅ 数据库连接初始化成功');
    testsPassed++;
    
    // 测试2: 验证各个数据库的collection操作
    const databases = ['admin', 'corp', 'Internet-hospital'];
    
    for (const dbName of databases) {
      testsTotal++;
      console.log(`\n2️⃣ 测试数据库 ${dbName} 的 collection 操作...`);
      
      try {
        const db = await getDatabase(dbName);
        
        // 这就是原始错误发生的地方: db.collection(...)
        // 如果db是undefined，这里就会抛出 "Cannot read properties of undefined (reading 'collection')"
        const testCollection = db.collection('test-verification');
        
        // 执行一些基本的collection操作
        const testDoc = { _id: 'verification-test', timestamp: new Date(), testData: 'sample' };
        
        // 插入
        await testCollection.insertOne(testDoc);
        console.log(`   ✅ ${dbName}: collection.insertOne() 成功`);
        
        // 查询
        const found = await testCollection.findOne({ _id: 'verification-test' });
        if (found) {
          console.log(`   ✅ ${dbName}: collection.findOne() 成功`);
        } else {
          throw new Error('查询结果为空');
        }
        
        // 更新
        await testCollection.updateOne(
          { _id: 'verification-test' }, 
          { $set: { updated: true, updateTime: new Date() } }
        );
        console.log(`   ✅ ${dbName}: collection.updateOne() 成功`);
        
        // 聚合查询
        const aggregateResult = await testCollection.aggregate([
          { $match: { _id: 'verification-test' } },
          { $project: { _id: 1, testData: 1 } }
        ]).toArray();
        
        if (aggregateResult.length > 0) {
          console.log(`   ✅ ${dbName}: collection.aggregate() 成功`);
        }
        
        // 删除
        await testCollection.deleteOne({ _id: 'verification-test' });
        console.log(`   ✅ ${dbName}: collection.deleteOne() 成功`);
        
        console.log(`✅ 数据库 ${dbName} 所有 collection 操作测试通过`);
        testsPassed++;
        
      } catch (error) {
        console.error(`❌ 数据库 ${dbName} collection 操作失败:`, error.message);
        if (error.message.includes("Cannot read properties of undefined (reading 'collection')")) {
          console.error('🚨 原始错误仍然存在！');
          throw error;
        }
      }
    }
    
    // 测试3: 验证错误处理机制
    testsTotal++;
    console.log('\n3️⃣ 测试错误处理机制...');
    
    try {
      // 尝试访问不存在的数据库
      const invalidDb = await getDatabase('non-existent-db');
      const collection = invalidDb.collection('test');
      console.log('✅ 即使是不常用的数据库也能正常创建collection');
      testsPassed++;
    } catch (error) {
      console.log('✅ 错误处理机制正常工作:', error.message);
      testsPassed++;
    }
    
    // 测试4: 并发collection操作
    testsTotal++;
    console.log('\n4️⃣ 测试并发 collection 操作...');
    
    const db = await getDatabase('admin');
    const concurrentPromises = [];
    
    for (let i = 0; i < 10; i++) {
      const promise = (async (index) => {
        const collection = db.collection(`concurrent-test-${index}`);
        await collection.insertOne({ _id: `test-${index}`, data: `concurrent-${index}` });
        const result = await collection.findOne({ _id: `test-${index}` });
        await collection.deleteOne({ _id: `test-${index}` });
        return result;
      })(i);
      
      concurrentPromises.push(promise);
    }
    
    const concurrentResults = await Promise.all(concurrentPromises);
    console.log(`✅ 并发操作成功，完成 ${concurrentResults.length} 个并发collection操作`);
    testsPassed++;
    
    // 测试5: 模拟原始应用场景
    testsTotal++;
    console.log('\n5️⃣ 模拟原始应用的典型操作...');
    
    const corpDb = await getDatabase('corp');
    const adminDb = await getDatabase('admin');
    const hlwDb = await getDatabase('Internet-hospital');
    
    // 模拟一些常见的业务操作
    const operations = [
      { db: corpDb, collection: 'member', doc: { _id: 'test-member', name: 'test' } },
      { db: adminDb, collection: 'groupmsg-task', doc: { _id: 'test-task', status: 'test' } },
      { db: hlwDb, collection: 'consult-record', doc: { _id: 'test-consult', type: 'test' } }
    ];
    
    for (const op of operations) {
      const collection = op.db.collection(op.collection);
      await collection.insertOne(op.doc);
      const found = await collection.findOne({ _id: op.doc._id });
      await collection.deleteOne({ _id: op.doc._id });
      console.log(`   ✅ ${op.collection}: 模拟业务操作成功`);
    }
    
    console.log('✅ 原始应用场景模拟测试通过');
    testsPassed++;
    
  } catch (error) {
    console.error('\n❌ 验证测试失败:', error.message);
    console.error('详细错误:', error);
    
    if (error.message.includes("Cannot read properties of undefined (reading 'collection')")) {
      console.error('\n🚨 原始错误未修复！数据库对象仍然为undefined');
      console.error('请检查：');
      console.error('1. MongoDB连接是否成功建立');
      console.error('2. getDatabase()函数是否正确返回数据库对象');
      console.error('3. 环境变量是否正确配置');
    }
    
    process.exit(1);
  } finally {
    await closeMongoDB();
    console.log('\n🔌 数据库连接已关闭');
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${testsPassed}/${testsTotal}`);
  console.log(`❌ 失败: ${testsTotal - testsPassed}/${testsTotal}`);
  
  if (testsPassed === testsTotal) {
    console.log('\n🎉 所有验证测试通过！');
    console.log('✅ 原始错误 "TypeError: Cannot read properties of undefined (reading \'collection\')" 已完全修复！');
    console.log('✅ 数据库连接稳定，collection操作正常');
    console.log('✅ 错误处理机制完善');
    console.log('✅ 并发操作支持良好');
    console.log('✅ 业务场景运行正常');
  } else {
    console.log('\n❌ 部分测试失败，问题可能未完全解决');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testFullVerification();
}

module.exports = { testFullVerification }; 