const dayjs = require("dayjs");
const { ObjectId } = require("mongodb");
const validator = require('../../utils/validator.js')

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getChargeList":
      return getChargeList(item);
    case "addHlwChargeItem":
      return addHlwChargeItem(item);
    case "deleteHlwChargeItem":
      return deleteHlwChargeItem(item);
    case "editHlwChargeItem":
      return editHlwChargeItem(item);
  }
}
async function getChargeList(ctx) {
  const page = Number.isInteger(ctx.page) && ctx.page > 0 ? ctx.page : 1;
  const pageSize = Number.isInteger(ctx.pageSize) && ctx.pageSize > 0 ? ctx.pageSize : 10;
  try {
    const query = {};
    if (typeof ctx.name === 'string' && ctx.name.trim()) {
      query.$or = [{ name: { $regex: ctx.name.trim() } }, { code: { $regex: ctx.name.trim() } }]
    }
    const res = await db.collection("charge-item-list").find(query).sort({ createTime: -1 }).skip((page - 1) * pageSize).limit(pageSize).toArray();
    const total = await db.collection("charge-item-list").countDocuments(query);
    return { success: true, data: res, total, message: '查询成功' }
  } catch (e) {
    return { success: false, msg: e.message }
  }
}

async function addHlwChargeItem(ctx) {
  try {
    const [valid, name] = validator.verifyString(ctx.name, "收费名称", 1);
    if (!valid) return { success: false, message: name }
    const [valid1, code] = validator.verifyString(ctx.code, "收费代码", 1);
    if (!valid1) return { success: false, message: code }
    const [valid2, fee] = validator.verifyNumber(ctx.fee, "收费金额", 2, 0, 9999999, true);
    if (!valid2) return { success: false, message: fee }
    const repeatCodeCount = await db.collection("charge-item-list").countDocuments({ code });
    if (repeatCodeCount > 0) {
      return { success: false, message: "收费编码已存在" }
    }
    const res = await db.collection("charge-item-list").insertOne({ name, code, fee, createTime: dayjs().valueOf() });
    return { success: true, data: res.insertedId, message: "新增成功" }
  } catch (e) {
    return { success: false, message: e.message }
  }
}


async function editHlwChargeItem(ctx) {
  const { id } = ctx;
  if (typeof id !== 'string' || !id.trim()) return { success: false, message: "id不能为空" };
  try {
    const [valid, name] = validator.verifyString(ctx.name, "收费名称", 1);
    if (!valid) return { success: false, message: name }
    const [valid1, code] = validator.verifyString(ctx.code, "收费代码", 1);
    if (!valid1) return { success: false, message: code }
    const [valid2, fee] = validator.verifyNumber(ctx.fee, "收费金额", 2, 0, 9999999, true);
    if (!valid2) return { success: false, message: fee }
    const repeatCodeCount = await db.collection("charge-item-list").countDocuments({ code, _id: { $ne: new ObjectId(id) } });
    if (repeatCodeCount > 0) {
      return { success: false, message: "收费编码重复" }
    }
    const res = await db.collection("charge-item-list").updateOne({ _id: new ObjectId(id) }, { $set: { name, code, fee, updateTime: dayjs().valueOf() } });
    return { success: true, data: res, message: "修改成功" }
  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function deleteHlwChargeItem(ctx) {
  const { id, code } = ctx;
  if (typeof id !== 'string' || !id.trim() || typeof code !== 'string' || !code.trim()) return { success: false, message: "id不能为空" };

  try {
    const useCodeStoreCount = await db.collection("store-list").countDocuments({ charge_id: code.trim() });
    if (useCodeStoreCount > 0) {
      return { success: false, message: "本条诊查费还有关联店铺在使用，请取消关联后再删除！" }
    }
    const res = await db.collection("charge-item-list").deleteOne({ _id: new ObjectId(id), code: code.trim() });
    return { success: true, data: res, message: "删除成功" }
  } catch (e) {
    return { success: false, message: e.message }
  }
}