const validator = require('../../utils/validator.js')

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getHlwOrderConfig":
      return await getHlwOrderConfig(item);
    case "updateHlwOrderConfig":
      return await updateHlwOrderConfig(item);
  }
};

async function getHlwOrderConfig({corpId}) {
  try {
    console.log("corpId", corpId);
    const res = await db.collection("hlw-config").findOne({ corpId }, { projection: { payDuration: 1, acceptDuration: 1, consultationDuration: 1, presOpen:1 } });
    if (!res) {
      return { success: false, message: "未找到配置" };
    }
    return { success: true, data: res, message: "获取配置成功" };
  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function updateHlwOrderConfig(ctx) {
  try {
    const { payDuration, acceptDuration, consultationDuration, presOpen } = ctx;
    const data = {}
    if (typeof payDuration == 'number') {
      const [valid, message] = validator.verifyNumber(payDuration, "支付时长", 0, 5, 60, true, true);
      if (!valid) {
        return { success: false, message };
      }
      data.payDuration = payDuration;
    }
    if (typeof acceptDuration == 'number') {
      const [valid, message] = validator.verifyNumber(acceptDuration, "接诊时长", 0, 5, 120, true, true);
      if (!valid) {
        return { success: false, message };
      }
      data.acceptDuration = acceptDuration;
    }
    if (typeof consultationDuration == 'number') {
      const [valid, message] = validator.verifyNumber(consultationDuration, "订单持续时长", 0, 10, 60 * 8, true, true);
      if (!valid) {
        return { success: false, message };
      }
      data.consultationDuration = consultationDuration;
    }
    if (typeof presOpen == 'boolean') {
      data.presOpen = presOpen;
    }
    if (Object.keys(data).length === 0) {
      return { success: false, message: "没有需要更新的配置" };
    }

    const res = await db.collection("hlw-config").updateOne({ corpId: ctx.corpId }, { $set: data });
    if (res.modifiedCount === 0) {
      return { success: false, message: "更新配置失败" };
    }
    return { success: true, message: "更新配置成功" };
  } catch (e) {
    return { success: false, message: e.message }
  }

}