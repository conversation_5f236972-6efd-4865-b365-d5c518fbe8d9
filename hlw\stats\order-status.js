
const payStatus = ['pending', 'success', 'cancelled', 'refunded', 'failed'];
const orderStatus = ['completed', 'cancelled', 'processing', 'finished', 'pending'];

const statusText = {
  'completed_success': '完成0单（尚未结束）',
  'processing_success': '进行中0单',
  'finished_success': '结束0单',
  'pending_success': '待接诊0单',
  'cancelled_refunded': '退款0单',
  'pending_pending': '发起但未支付0单',
  'cancelled_cancelled': '超时未支付取消0单',
  'cancelled_success': '订单超时取消0单',
  'cancelled_failed': '支付失败取消0单',
  'pending_failed': '发起但支付失败0单'
}

module.exports = function (list, doctList) {
  const statsList = Array.isArray(list) ? list : [];
  // tongji
  if (Array.isArray(doctList)) {
    const doctM = doctList.reduce((m, item) => {
      if (item.doctorNo) {
        item.allCount = 0;
        m[item.doctorNo] = item;
      }
      return m;
    }, {})
    const unset = [];
    statsList.forEach(item => {
      const [key, count, text] = getCountText(item);
      if (doctM[item.doctorCode]) {
        doctM[item.doctorCode][key] = text;
        doctM[item.doctorCode].allCount = doctM[item.doctorCode].allCount + count;
      } else {
        unset.push(item);
      }
    })
    return [...Object.values(doctM).sort((a, b) => b.allCount - a.allCount), ...unset];
  } else {
    return statsList.map(item => {
      const [key, count, text] = getCountText(item);
      return {
        ...item,
        key,
        text
      }
    })
  }

}

function getCountText({ orderStatus, payStatus, count }) {
  const key = `${orderStatus}_${payStatus}`;
  if (statusText[key]) {
    return [key, count, statusText[key].replace('0单', `${count}单`)];
  }
  return [key, count, `${count}单`]

}