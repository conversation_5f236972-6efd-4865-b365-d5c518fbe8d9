let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getSuiteToken":
      return await getSuiteToken();
    case "updateSuiteToken":
      return await updateSuiteToken(event);
  }
};
// 获取 SuiteToken
async function getSuiteToken() {
  try {
    const res = await db
      .collection("weComToken")
      .findOne({ type: "suiteToken" });
    if (!res) {
      return {
        success: false,
        message: "SuiteToken 不存在",
      };
    }
    return {
      success: true,
      suiteToken: res.suite_token,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: `获取失败: ${error.message}`,
    };
  }
}

// 更新 SuiteToken
async function updateSuiteToken({ suiteToken }) {
  try {
    const result = await db.collection("weComToken").updateOne(
      { type: "suiteToken" },
      { $set: { suite_token: suiteToken } },
      { upsert: true } // 如果不存在，则插入
    );
    return {
      success: true,
      message: "更新成功",
      result,
    };
  } catch (error) {
    return {
      success: false,
      message: `更新失败: ${error.message}`,
    };
  }
}
