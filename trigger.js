const schedule = require("node-schedule");
const corpId = ""; // 私有化部署的corpId
const api = require("./api");
const logger = require("./utils/logger");

exports.initSchedule = () => {
  console.log("定时任务触发");
  batchStorageSessionTrigger();
  triggeredAt7am();
  triggeredAt6am();
  triggeredAt8am();
};

function batchStorageSessionTrigger() {
  schedule.scheduleJob("0 0 * * * * *", async () => {
    await batchStorageSession();
    await batchCreateGroupMsgTaskByTodo();
  });
}

function triggeredAt6am() {
  schedule.scheduleJob("0 0 6 * * * *", async () => {
    await customerSopTaskTrigger();
  });
}

// 凌晨7点触发
function triggeredAt7am() {
  schedule.scheduleJob("0 0 7 * * * *", async () => {
    await updateExpireStatus();
    await managementPlanTaskToEvents();
    await pushtoDoCountToCorpApp();
    await getAllCorpYesterdayBehaviorData();
  });
}

function triggeredAt8am() {
  schedule.scheduleJob("0 0 8 * * * *", async () => {
    await createCurrentGroupMsgTask();
    await stopExpireGroupmsgTask();
  });
}

async function batchStorageSession() {
  const params = {
    type: "batchStorageSession",
    corpId,
  };
  const res = await api.getSessionArchive(params);
  logger.info(res);
}

// 更新待办事项过期状态
async function updateExpireStatus() {
  const params = {
    type: "updateExpireStatus",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}
//管理计划任务每日定时触发生成待办事件
// managementPlanTaskToEvents
async function managementPlanTaskToEvents() {
  const params = {
    type: "managementPlanTaskToEvents",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}
async function customerSopTaskTrigger() {
  const params = {
    type: "customerSopTaskTrigger",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}

// 推送待办任务到应用
async function pushtoDoCountToCorpApp() {
  const params = {
    type: "pushtoDoCountToCorpApp",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}
// 创建当前群发任务
async function createCurrentGroupMsgTask() {
  const params = {
    type: "pushtoDoCountToCorpApp",
    corpId,
  };
  const res = await api.getMemberApi(params);
  logger.info(res);
}

// 停止过期的群发任务
async function stopExpireGroupmsgTask() {
  const params = {
    type: "stopExpireGroupmsgTask",
    corpId,
  };
  const res = await api.getMemberApi(params);
  logger.info(res);
}

async function getAllCorpYesterdayBehaviorData() {
  const params = {
    type: "getAllCorpYesterdayBehaviorData",
  };
  const res = await api.getWecomApi(params);
  logger.info(res);
}

async function batchCreateGroupMsgTaskByTodo() {
  const params = {
    type: "batchCreateGroupMsgTaskByTodo",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}
