#!/usr/bin/env node

/**
 * 真实世界场景测试
 * 模拟实际应用中的常见操作，验证修复的有效性
 */

// 加载环境变量
const envFile = `.env.${process.env.NODE_ENV || "development"}`;
require("dotenv").config({ path: envFile });

const express = require("express");
const { connectToMongoDB, getDatabase, closeMongoDB } = require('./mongodb');

async function testRealWorldScenario() {
  console.log('🌍 开始真实世界场景测试...');
  
  let client;
  let server;
  
  try {
    // 1. 模拟应用启动
    console.log('\n1️⃣ 模拟应用启动流程...');
    
    // 连接数据库
    await connectToMongoDB();
    console.log('✅ 数据库连接建立');
    
    // 创建Express应用
    const app = express();
    app.use(express.json());
    
    // 2. 添加真实的路由处理器
    console.log('\n2️⃣ 添加真实路由处理器...');
    
    // 模拟 /getYoucanData/member 路由
    app.post('/getYoucanData/member', async (req, res) => {
      try {
        const db = await getDatabase("admin");
        // 这里是原始错误最可能发生的地方
        const collection = db.collection("member");
        const result = await collection.find({}).limit(1).toArray();
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
    
    // 模拟 /getYoucanData/corp 路由
    app.post('/getYoucanData/corp', async (req, res) => {
      try {
        const db = await getDatabase("corp");
        const collection = db.collection("corp-info");
        const result = await collection.find({}).limit(1).toArray();
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
    
    // 模拟 /getYoucanData/hlw 路由
    app.post('/getYoucanData/hlw', async (req, res) => {
      try {
        const db = await getDatabase("Internet-hospital");
        const collection = db.collection("hlw-config");
        const result = await collection.find({}).limit(1).toArray();
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
    
    // 健康检查端点
    app.get('/health', async (req, res) => {
      try {
        // 测试所有数据库连接
        const adminDb = await getDatabase("admin");
        const corpDb = await getDatabase("corp");
        const hlwDb = await getDatabase("Internet-hospital");
        
        // 测试 collection 操作
        await adminDb.collection("test").findOne({});
        await corpDb.collection("test").findOne({});
        await hlwDb.collection("test").findOne({});
        
        res.json({ 
          status: 'healthy', 
          databases: ['admin', 'corp', 'Internet-hospital'],
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({ status: 'unhealthy', error: error.message });
      }
    });
    
    // 启动服务器
    const port = process.env.CONFIG_NODE_PORT || 3000;
    server = app.listen(port, () => {
      console.log(`✅ 服务器启动成功，端口: ${port}`);
    });
    
    // 等待服务器完全启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 测试API端点
    console.log('\n3️⃣ 测试API端点...');
    
    const testEndpoints = [
      { method: 'GET', path: '/health', description: '健康检查' },
      { method: 'POST', path: '/getYoucanData/member', body: {}, description: '会员数据' },
      { method: 'POST', path: '/getYoucanData/corp', body: {}, description: '企业数据' },
      { method: 'POST', path: '/getYoucanData/hlw', body: {}, description: '互联网医院数据' }
    ];
    
    for (const endpoint of testEndpoints) {
      try {
        const axios = require('axios');
        const url = `http://localhost:${port}${endpoint.path}`;
        
        let response;
        if (endpoint.method === 'GET') {
          response = await axios.get(url, { timeout: 5000 });
        } else {
          response = await axios.post(url, endpoint.body, { timeout: 5000 });
        }
        
        console.log(`   ✅ ${endpoint.description}: ${response.status} - ${response.data?.success !== false ? '成功' : '失败'}`);
        
        // 检查是否有collection相关错误
        if (response.data?.error && response.data.error.includes('collection')) {
          throw new Error(`Collection错误: ${response.data.error}`);
        }
        
      } catch (error) {
        if (error.message.includes("Cannot read properties of undefined (reading 'collection')")) {
          console.error(`   ❌ ${endpoint.description}: 原始错误仍然存在!`);
          throw error;
        } else {
          console.log(`   ⚠️  ${endpoint.description}: ${error.message} (这可能是正常的，因为没有实际数据)`);
        }
      }
    }
    
    // 4. 压力测试
    console.log('\n4️⃣ 进行压力测试...');
    
    const concurrentRequests = [];
    for (let i = 0; i < 20; i++) {
      const request = (async () => {
        try {
          const axios = require('axios');
          const response = await axios.get(`http://localhost:${port}/health`, { timeout: 10000 });
          return response.status === 200;
        } catch (error) {
          if (error.message.includes("Cannot read properties of undefined (reading 'collection')")) {
            throw error;
          }
          return false;
        }
      })();
      
      concurrentRequests.push(request);
    }
    
    const results = await Promise.all(concurrentRequests);
    const successCount = results.filter(r => r === true).length;
    console.log(`✅ 压力测试完成: ${successCount}/20 请求成功`);
    
    // 5. 数据库操作压力测试
    console.log('\n5️⃣ 数据库操作压力测试...');
    
    const dbOperations = [];
    const databases = ['admin', 'corp', 'Internet-hospital'];
    
    for (let i = 0; i < 30; i++) {
      const operation = (async (index) => {
        const dbName = databases[index % databases.length];
        const db = await getDatabase(dbName);
        const collection = db.collection(`stress-test-${index}`);
        
        // 执行CRUD操作
        await collection.insertOne({ _id: `stress-${index}`, data: `test-${index}`, timestamp: new Date() });
        const found = await collection.findOne({ _id: `stress-${index}` });
        await collection.updateOne({ _id: `stress-${index}` }, { $set: { updated: true } });
        await collection.deleteOne({ _id: `stress-${index}` });
        
        return found !== null;
      })(i);
      
      dbOperations.push(operation);
    }
    
    const dbResults = await Promise.all(dbOperations);
    const dbSuccessCount = dbResults.filter(r => r === true).length;
    console.log(`✅ 数据库压力测试完成: ${dbSuccessCount}/30 操作成功`);
    
    console.log('\n🎉 真实世界场景测试全部通过！');
    console.log('✅ 应用在真实使用场景下运行正常');
    console.log('✅ 所有API端点响应正常');
    console.log('✅ 压力测试表现良好');
    console.log('✅ 数据库操作稳定可靠');
    
  } catch (error) {
    console.error('\n❌ 真实世界场景测试失败:', error.message);
    
    if (error.message.includes("Cannot read properties of undefined (reading 'collection')")) {
      console.error('🚨 原始错误在真实场景中仍然存在！');
    }
    
    process.exit(1);
  } finally {
    // 清理资源
    if (server) {
      server.close();
      console.log('🔌 服务器已关闭');
    }
    await closeMongoDB();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testRealWorldScenario();
}

module.exports = { testRealWorldScenario }; 