const dayjs = require("dayjs");
const ToDoItem = {
  corpId: "机构id不能为空",
  customerId: "客户id不能为空", // 服务对象id
  customerName: "客户姓名不能为空", // 服务对象姓名
  expireTime: "过期时间不能为空", // 过期时间
  plannedExecutionTime: "", // 计划执行时间
  externalUserId: "", // 服务对象的外部联系人id 外部联系人id不能为空
  taskContent: "待办内容不能为空", // 待办内容
  createTime: "创建时间不能为空", // 创建时间
  creatorUserId: "创建人不能为空", // 创建人
  eventStatus: "待办状态不能为空", // 待办状态
  eventType: "待办类型不能为空", // 待办类型
  executorUserId: "执行人不能为空", // 执行人userId
  executeTeamId: "执行团队不能为空",
};

exports.getTodoItem = function (data) {
  const keys = Object.keys(ToDoItem);
  const key = keys.find((key) => !data[key]);
  if (key) {
    return { success: false, message: ToDoItem[key] };
  }
  return {
    success: true,
    data: keys.reduce((val, key) => ((val[key] = data[key]), val), {}),
  };
};

exports.createTodoEvents = (params) => {
  const currentTime = new Date();
  // 设置目标时间为当前时间的下一天的 7 点
  const {
    corpId = "",
    executorUserId = "",
    customerId = "",
    customerName = "",
    pannedSendContent = "",
    pannedEventSendFile = {},
    taskContent = "",
    pannedEventName = "",
    pannedEventId = "",
    teamId = "",
    taskId = "",
    memberPlanId = "",
    planId = "",
    planExecutionTime = "",
    executeTeamId = "",
    sendContent = "",
    executeTeamName = "",
    customerUserId = "",
    planName,
    eventType,
    creatorUserId,
    executeMethod,
    enableSend,
    fileList,
  } = params;
  let item = {
    corpId,
    createTime: currentTime.getTime(),
    creatorUserId: creatorUserId || "system",
    customerId, // 服务对象姓名
    customerName,
    pannedEventSendFile, // 发送的附件
    pannedEventId,
    executeTeamName,
    customerUserId,
    isFeedback: false,
    executeTeamId,
    executorUserId,
    eventType,
  };
  // 如果是任务的话 说明此任务是管理计划生成的
  if (taskId) {
    item["taskId"] = taskId;
    item["planId"] = planId;
    item["memberPlanId"] = memberPlanId;
    item["plannedExecutionTime"] = planExecutionTime;
    item["taskContent"] = taskContent;
    item["sendContent"] = sendContent; // 任务内容
    item["planName"] = planName;
    item["executeMethod"] = executeMethod;
    item["enableSend"] = enableSend;
    item["fileList"] = Array.isArray(fileList) ? fileList : [];
    item["_type"] = "plan-task-todo";
  } else {
    item["plannedExecutionTime"] = dayjs(currentTime.getTime()).valueOf();
    item["taskContent"] = pannedEventName || taskContent;
    item["pannedEventName"] = pannedEventName;
    item["sendContent"] = pannedSendContent; // 任务内容
    item["executeTeamId"] = teamId;
  }
  // expireTime 过期时间是任务开始时间往后推7天
  item["expireTime"] = dayjs(item["plannedExecutionTime"])
    .add(7, "day")
    .valueOf();
  // 任务状态是未处理
  item["eventStatus"] = "untreated";
  return item;
};

exports.processInBatches = async (arr, handler, batchSize = 10) => {
  const result = [];
  for (let i = 0; i < arr.length; i += batchSize) {
    const batch = arr.slice(i, i + batchSize);
    const batchResult = await Promise.all(batch.map(handler));
    result.push(...batchResult);
  }
  return result;
};

exports.getAllData = async (fetchData, db, pageSize = 100) => {
 
  let page = 1;
  let allData = [];
  while (true) {
    let list = await fetchData(page, pageSize, db);
    console.log("list", list);
    if (list.length > 0) {
      allData.push(...list);
      page++;
    } else {
      break;
    }
  }
  return allData;
};
