const dayjs = require("dayjs");
const utils = require("../utils");
let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getCorpMemberChatList":
      return await exports.getCorpMemberChatList(event);
    case "getCorpMemberChatRecord":
      return await exports.getCorpMemberChatRecord(event);
    case "getChatCount":
      return await getChatCount(event);
    case "getToDayAllChatRecord":
      return await exports.getToDayAllChatRecord(event);
    case "getReplyChatRecord":
      return await exports.getReplyChatRecord(event);
  }
};

// 会话统计
async function getChatCount(event) {
  let { corpId = "" } = event;
  const todayStartTime = dayjs().startOf("day").valueOf();
  const todayEndTime = dayjs().endOf("day").valueOf();
  const chatTotalCount = await db
    .collection("session-archive-record")
    .countDocuments({ corpId });
  // 获取今天聊天记录的总数
  const todayChatCount = await db
    .collection("session-archive-record")
    .countDocuments({
      corpId,
      createTime: { $gte: todayStartTime, $lt: todayEndTime },
    });

  return {
    success: true,
    message: "获取成功",
    chatTotalCount: chatTotalCount || 0,
    todayChatCount: todayChatCount || 0,
  };
}

// 获取机构成员聊天对象列表
exports.getCorpMemberChatList = async (event) => {
  const { corpId = "", memberUserId = "", page = 1, pageSize = 10 } = event;
  // 分页查询条件
  const query = {
    corpId,
    memberUserId,
  };
  // 获取总数
  const total = await db
    .collection("session-archive-user")
    .countDocuments(query);
  // 获取分页数据
  const res = await db
    .collection("session-archive-user")
    .aggregate([
      { $match: query }, // 匹配条件
      { $sort: { sendTime: -1 } }, // 按发送时间降序排序
      { $skip: pageSize * (page - 1) }, // 跳过前面的数据（分页）
      { $limit: pageSize }, // 限制返回的数据数量
      {
        $lookup: {
          from: "wechat-friends", // 要连接的集合
          localField: "customerUserId", // 当前集合的字段
          foreignField: "external_userid", // 要连接的集合的字段
          as: "wechatFriends",
        },
      },
      {
        $project: {
          corpId: 1,
          customerUserId: 1,
          memberUserId: 1,
          sendTime: 1,
          "memberList._id": 1,
          "memberList.name": 1,
          externalUserName: { $arrayElemAt: ["$wechatFriends.name", 0] },
        },
      }, // 选择要返回的字段
    ])
    .toArray(); // 执行并转换为数组
  // 计算总页数
  const pages = Math.ceil(total / pageSize);
  return {
    success: true,
    message: "获取成功",
    data: res,
    total: total,
    pages: pages,
    size: pageSize,
  };
};

// 获取机构成员与客户的聊天记录
exports.getCorpMemberChatRecord = async (event) => {
  const {
    corpId = "",
    memberUserId = "",
    customerUserId = "",
    msgIds = [],
    page = 1,
    pageSize = 10,
    orderBy = "asc",
  } = event;

  // 分页
  const skip = (page - 1) * pageSize;
  const limit = pageSize;

  // 构建查询条件
  const query = { corpId };
  if (memberUserId) query.memberUserId = memberUserId;
  if (customerUserId) query.customerUserId = customerUserId;
  if (msgIds.length > 0) query.msgid = { $in: msgIds };

  // 获取总数
  const total = await db
    .collection("session-archive-record")
    .countDocuments(query);
  // 获取分页数据
  const res = await db
    .collection("session-archive-record")
    .find(query)
    .sort({ sendTime: orderBy === "asc" ? 1 : -1 }) // 根据orderBy字段排序
    .skip(skip) // 跳过的文档数
    .limit(limit) // 限制返回的文档数
    .project({
      corpId: 1,
      customerUserId: 1,
      memberUserId: 1,
      sendTime: 1,
      msgid: 1,
      // 这里可以根据需要选择其他字段
    })
    .toArray(); // 转换为数组
  // 计算总页数
  const pages = Math.ceil(total / pageSize);
  return {
    success: true,
    message: "获取成功",
    data: res,
    total: total,
    pages: pages,
    size: pageSize,
  };
};

exports.getToDayAllChatRecord = async (event) => {
  let { corpId = "", memberUserId = "", customerUserId = "" } = event;
  const todayStartTime = dayjs().startOf("day").unix();
  const todayEndTime = dayjs().endOf("day").unix();
  let query = {
    corpId,
    memberUserId,
    customerUserId,
    sendTime: { $gte: todayStartTime, $lt: todayEndTime },
  };
  const fetchData = async (page, pageSize, db) => {
    const data = await db
      .collection("session-archive-record")
      .find(query)
      .sort({ sendTime: 1 }) // 按 sendTime 升序排序
      .skip((page - 1) * pageSize) // 跳过前面的数据
      .limit(pageSize) // 限制返回的数据数量
      .project({
        msgid: 1,
        encryptedSecretKey: 1,
        publicKeyVersion: 1,
      }) // 选择返回的字段
      .toArray(); // 转换为数组
    return data;
  };

  // 获取所有数据
  const list = await utils.getAllData(fetchData, db);
  return {
    success: true,
    message: "获取成功",
    data: list,
  };
};

exports.getReplyChatRecord = async (event) => {
  const { corpId = "", memberUserId = "", customerUserId = "" } = event;

  // 构建查询条件
  const query = {
    corpId,
    receiverUserId: memberUserId,
    senderUserId: customerUserId,
  };

  // 获取最新回复时间
  const latestSendtimeRes = await db
    .collection("latest-reply-time")
    .find({ memberUserId, customerUserId, corpId })
    .toArray();

  let latestSendtime = "";
  let secondLastTime = "";

  if (latestSendtimeRes.length > 0) {
    latestSendtime = latestSendtimeRes[0].latestSendtime;
    secondLastTime = latestSendtimeRes[0].secondLastTime;
    query.sendTime = { $gt: latestSendtime }; // 使用 MongoDB 的 $gt 操作符
  }

  // 获取总数
  let count = await db.collection("session-archive-record").find(query).count();
  if (count === 0) {
    if (secondLastTime) {
      query.sendTime = { $gt: secondLastTime }; // 如果没有数据，用第二次的时间过滤
    } else {
      delete query.sendTime; // 如果没有第二次时间，删除 sendTime 查询条件
    }
    latestSendtime = secondLastTime; // 更新 latestSendtime 为 secondLastTime
  }
  // 获取最新的回复消息
  const res = await db
    .collection("session-archive-record")
    .find(query)
    .sort({ sendTime: -1 }) // 按 sendTime 降序排序
    .skip(0) // 分页：从第一页开始
    .limit(10) // 每页 10 条数据
    .project({
      msgid: 1,
      encryptedSecretKey: 1,
      publicKeyVersion: 1,
      sendTime: 1,
    }) // 选择返回字段
    .toArray(); // 执行查询并转换为数组

  const latestSendData = res[0] || {};

  if (latestSendData.sendTime) {
    await updateLatestSendTime({
      corpId,
      memberUserId,
      customerUserId,
      latestSendtime: latestSendData.sendTime,
      secondLastTime: latestSendtime,
      latestSendtimeRes,
    });
  }

  return {
    success: true,
    message: "获取成功",
    data: res,
  };
};

async function updateLatestSendTime({
  corpId,
  memberUserId,
  customerUserId,
  latestSendtime,
  secondLastTime,
  latestSendtimeRes,
}) {
  console.log("updateLatestSendTime", latestSendtimeRes);

  // 检查是否已经存在记录
  if (latestSendtimeRes.length > 0) {
    // 更新现有记录
    await db.collection("latest-reply-time").updateOne(
      { corpId, memberUserId, customerUserId }, // 查询条件
      { $set: { latestSendtime, secondLastTime } } // 更新字段
    );
  } else {
    // 插入新记录
    await db.collection("latest-reply-time").insertOne({
      corpId,
      memberUserId,
      customerUserId,
      latestSendtime,
      secondLastTime,
    });
  }
}
