const dayjs = require("dayjs");
const { decode } = require('iconv-lite');
const request = require("../../request");

// const baseUrl = "http://**************:8089"; //his说 8089可能会关闭，用8083
const baseUrl = "http://**************:8083";

const AppWin = {
  line: '线上挂号',
  win: '线下挂号',
}

module.exports = async (item) => {
  switch (item.type) {
    case "getHisCustomer":
      return await getHisCustomer(item);
    case "addHisCustomer":
      return await addHisCustomer(item);
    case "registration":
      return await registration(item);
    case "getPayStatus":
      return await getPayStatus(item);
    case "hlwRefund":
      return await hlwRefund(item);
    case "hlwuploadprescription":
      return await hlwuploadprescription(item);
    case "hlwPrescriptionOrderStatus":
      return await hlwPrescriptionOrderStatus(item);
    case "getHisPrescriptionUploadStatus":
      return await getHisPrescriptionUploadStatus(item);
    case "getHisStoreRegList":
      return await getHisStoreRegList(item);
    case "insurancePreSettle":
      return await insurancePreSettle(item);
    case "insuranceSettle":
      return await insuranceSettle(item);
    case "insuranceRefund":
      return await insuranceRefund(item);
    case "getRecent90daysDrugRecord":
      return await getRecent90daysDrugRecord(item);
  }
};
async function getHisCustomer(item) {
  const { idCard } = item;
  try {
    const url = `${baseUrl}/his/hlw/patientinfo`;
    const { data, status_code, message } = await request.main(
      url,
      { type: "01", idnumber: idCard },
      "POST"
    );
    if (status_code == 200) {
      return {
        success: true,
        list: data?.patients,
        message: "获取成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: e.message || "获取失败",
    };
  }
}
/**
 * 
 * @param {buildType} 建档类型 1.自费建档  2.电子凭证建档 3.线下医保建档 
 * @returns 
 */
async function addHisCustomer(item) {
  let { idCard, name, mobile, address, psnToken, buildType = '1', mdtrt_cert_type, mdtrt_cert_no, card_sn } = item;
  try {
    const url = `${baseUrl}/his/hlw/patientbulid`;
    const payload = {
      type: buildType,
      name,
      socialno: idCard,
      tel: mobile,
    };
    if (typeof address === "string") {
      payload.address = address;
    }
    // 电子凭证建档 医保插件授权建档
    if (psnToken) {
      payload.psnToken = psnToken;
      payload.type = "2";
    }
    // 线下医保建档 
    // mdtrt_cert_type 就诊凭证类型  01 | 03
    // mdtrt_cert_no 就诊凭证号码 mdtrt_cert_type为01 填写电子凭证令牌 | mdtrt_cert_type为03 填写社会保障卡卡号
    if (mdtrt_cert_type) {
      payload.mdtrt_cert_type = mdtrt_cert_type;
      payload.mdtrt_cert_no = mdtrt_cert_no;
      payload.card_sn = card_sn; //就诊凭证类型为“03”时填写社会保障卡内码
      payload.type = "3";
    }

    const { data, status_code, message } = await request.main(
      url,
      payload,
      "POST"
    );
    if (status_code == 200 && !message) {
      return {
        success: true,
        list: data?.patients,
        message: "获取成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

// 预约挂号
async function registration(item) {
  const { patientId, doctorCode, appwin = 'line', unitCode, registerId, chargecode, reg_fee, shopid, shopname, op_id = '' } = item;
  if (AppWin[appwin] == undefined) {
    return {
      success: false,
      message: "appwin参数错误"
    }
  }
  if (AppWin[appwin] == '线下挂号' && (typeof op_id !== 'string' || op_id.trim() === '')) {
    return {
      success: false,
      message: "op_id参数错误"
    }
  }
  try {
    const url = `${baseUrl}/his/hlw/registration`;
    // 根据dayjs 判断是上午 还是下午
    const ampm = dayjs().hour() < 12 ? "a" : "p";
    const requestday = dayjs().format("YYYY-MM-DD HH:mm:ss");
    // registerId 为 H开头 15位随机数
    const payload = {
      patientId,
      doctorCode,
      unitCode,
      requestday,
      ampm,
      registerId,
      chargecode,
      reg_fee,
      shopid,
      shopname,
      chargetype: "普通",
      appwin
    };
    if (AppWin[appwin] == '线下挂号') {
      payload.op_id = op_id;
    }
    const { data, status_code, message } = await request.main(
      url,
      payload,
      "POST"
    );
    if (data && data.register && data.register.medorg_order_no) {
      return {
        success: true,
        data,
        message: message || "挂号成功",
      };
    } else {
      return {
        success: false,
        message: message || "挂号失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: err.message || "挂号失败"
    };
  }
}

//订单支付状态查询

async function getPayStatus(item) {
  const { patientId, registerId, medorgOrderNo } = item;
  try {
    const url = `${baseUrl}/his/hlw/settlement`;
    const { status, status_code, message, ...rest } = await request.main(
      url,
      {
        patientid: patientId,
        registerid: registerId,
        medorg_order_no: medorgOrderNo,
      },
      "POST"
    );
    const settlement =
      rest.data && rest.data.settlement ? rest.data.settlement : null;
    //settlement.status  0 已收费 1 已退费 5 未收费 x 已作废
    if (status_code == 200) {
      return {
        success: true,
        status: settlement ? settlement.status : '',
        settlement,
        message: "订单查询成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

//医保患者退费
async function hlwRefund(item) {
  const { patientId, registerId, medorgOrderNo } = item;
  try {
    const url = `${baseUrl}/his/hlw/refund`;
    const { data, status_code, message, ...rest } = await request.main(
      url,
      {
        patientid: patientId,
        registerid: registerId,
        medorg_order_no: medorgOrderNo,
      },
      "POST"
    );
    if (status_code == 200 && !message) {
      return {
        success: true,
        message: "退费成功",
      };
    } else {
      return {
        success: false,
        message: message || "退费失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "退费失败",
    };
  }
}

// 6.外配处方回写
/**
 *
 * @param {*} param0
 * @returns
 */
async function hlwuploadprescription(item) {
  try {
    const url = `${baseUrl}/his/hlw/ordersave`;
    const { code, status_code, message } = await request.main(
      url,
      item.payload,
      "POST"
    );

    if (status_code == 200 && code == '0' && !message) {
      return {
        success: true,
        _id: item._id,
        message: "回写成功",
      };
    } else {
      return {
        success: false,
        message: message || "回写失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: err.message || "回写失败",
    };
  }
}

// 外配处方状态查询

async function hlwPrescriptionOrderStatus(item) {
  const { patientId, orderno } = item;
  try {
    const url = `${baseUrl}/his/hlw/orderstatus`;
    const { status_code, message, data } = await request.main(
      url,
      {
        patientid: patientId,
        orderno,
      },
      "POST"
    );
    if (status_code == 200 && data && data.orderstatus) {
      return {
        success: true,
        uploaded: data.orderstatus.status == '1',
        status: data.orderstatus.status,
        message: "查询成功",
      };
    } else {
      return {
        success: false,
        message: message || "查询失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }
}

async function getHisPrescriptionUploadStatus(item) {
  const { patientId, orderno, _id } = item;
  try {
    const url = `${baseUrl}/his/hlw/orderstatus`;
    const { status_code, message, data } = await request.main(
      url,
      {
        patientid: patientId,
        orderno,
      },
      "POST"
    );
    if (status_code == 200 && data && data.orderstatus && data.orderstatus.status == 1) {
      return { orderno, hisUploadStatus: 'uploaded', _id }
    }
    if (status_code == 200 && data && data.orderstatus && data.orderstatus.status == -1) {
      return { orderno, hisUploadStatus: 'error', _id }
    }
    if (status_code == 200 && data && data.orderstatus && data.orderstatus.status == 0) {
      return {}
    }
    return {
      success: false,
      message: message || "查询失败",
    };
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }

}


async function getHisStoreRegList(item) {
  const { storeId: shopid } = item;
  if (typeof shopid !== 'string' || shopid.trim() === '') {
    return {
      success: false,
      message: "店铺id无效",
    };
  }
  try {
    const url = `${baseUrl}/his/hlw/registerquery`;
    const { status_code, message, data } = await request.main(
      url,
      {
        shopid,
        requestday: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        status: "0",
        starttime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endtime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        hospid: "108",
      },
      "POST"
    );
    if (status_code == 200 && Array.isArray(data)) {
      return {
        success: true,
        list: data,
        message: "查询成功",
      };
    } else {
      return {
        success: false,
        message: message || "查询失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }
}


async function insurancePreSettle(ctx) {
  const {
    medorg_order_no: orderno,
    patientId: patientid,
    mdtrtCertNo: mdtrt_cert_no,
    mdtrtCertType: mdtrt_cert_type,
    cardSn: card_sn,
    operator_code,
    operator_name,
    shopid,
    hospid = "108"
  } = ctx;
  try {
    const url = `${baseUrl}/his/hlw/presettle`;
    const payload = { orderno, patientid, mdtrt_cert_no, mdtrt_cert_type, card_sn, operator_code, operator_name, hospid, shopid };
    const { data, status_code, message } = await request.main(url, payload, "POST");

    if (status_code == 200 && !message) {
      return { success: true, data, message: '预结算成功' }
    }
    return { success: false, message: message || '预结算失败' }
  } catch (e) {
    return { success: false, message: e.message || '预结算失败' }
  }
}


async function insuranceSettle(ctx) {
  const {
    patientid,
    orderno,
    mdtrt_id,
    mdtrt_cert_no,
    outparm_1101,
    outparm_2206A,
    inparm_2207A,
    outparm_2204,
    totalamount,
    selfpay,
    operator_code,
    operator_name
  } = ctx;
  const payload = {
    patientid,
    orderno,
    settle_method: '7',
    paymethod: '1', // 1: 现金 w: 支付宝 z: 微信
    paysn: '',
    payrequestsn: '',
    mdtrt_id,
    mdtrt_cert_no,
    outparm_1101,
    outparm_2206A,
    inparm_2207A,
    outparm_2204,
    totalamount,
    selfpay,
    operator_code,
    operator_name
  }
  try {
    const url = `${baseUrl}/his/hlw/ybsettle`;
    // const url = `${baseUrl}/his/hlw/presettle`;
    const { data, status_code, message } = await request.main(url, payload, "POST");
    if (status_code == 200 && !message) {
      return { success: true, data, message: '结算成功' }
    }
    return { success: false, message: message || '结算失败' }
  } catch (e) {
    return { success: false, message: e.message || '结算失败' }
  }
}

async function insuranceRefund(ctx) {
  const { type, ...payload } = ctx;
  try {
    const url = `${baseUrl}/his/hlw/ybrefund`;
    // const url = `${baseUrl}/his/hlw/presettle`;
    const { data, status_code, message } = await request.main(url, payload, "POST");
    if (status_code == 200 && !message) {
      return { success: true, data, message: '退费成功' }
    }
    return { success: false, message: message || '退费失败' }
  } catch (e) {
    return { success: false, message: e.message || '退费失败' }
  }
}

async function getRecent90daysDrugRecord(ctx) {
  const patientid = typeof ctx.patientId === 'string' ? ctx.patientId.trim() : '';
  if (patientid === '') {
    return { success: false, message: '患者ID不能为空' }
  }
  try {
    const url = `${baseUrl}/his/hlw/patientzj3100`;
    const payload = { patientid, hospid: "108", doctorCode: '10021' };
    if (typeof ctx.doctorcode === 'string' && ctx.doctorcode.trim() !== '') {
      payload.doctorcode = ctx.doctorcode.trim();
    }
    const { data, status_code, message } = await request.main(url, payload, "POST");


    if (status_code == 200 && !message && data && data.outparm_zj3100) {
      // 解析医保用药数据列表
      const decodedBuffer = Buffer.from(data.outparm_zj3100, 'base64');
      const gbkDecodedStr = decode(decodedBuffer, 'gbk');
      const res = JSON.parse(gbkDecodedStr);
      const drugList = res && res.data && Array.isArray(res.data.lists) ? res.data.lists.map(i => ({
        hiListName: i.hiListName,
        mdtrtId: i.mdtrtId,
      })) : [];

      // 解析医保记录
      const decodedBuffer1 = Buffer.from(data.outparm_zj3101, 'base64');
      const gbkDecodedStr1 = decode(decodedBuffer1, 'gbk');
      const res1 = JSON.parse(gbkDecodedStr1);
      const recordList = res1 && res1.data && Array.isArray(res1.data.lists) ? res1.data.lists.map(i => ({
        mdtrtId: i.mdtrtId,
        date: i.admDate,
        medinsName: i.medinsName,
        mdtrtId: i.mdtrtId,
      })) : [];

      return { success: true, drugList, message: '查询成功', recordList }
    }
    return { success: false, message: message || '查询失败' }
  } catch (e) {
    return { success: false, message: e.message || '查询失败' }
  }
}