const axios = require("axios");
const fs = require("fs");
const FormData = require("form-data");
const token = require("../token");
exports.main = async (content) => {
  switch (content.type) {
    case "uploadTempMedia":
      return await this.uploadTempMedia(content);
    case "uploadTempImage":
      return await this.uploadTempImage(content);
  }
};
exports.uploadTempMedia = async (content) => {
  const { sendType, corpId, name, file } = content;
  let formData = new FormData();
  const accessToken = await token.getToken({ corpId });
  const fileStream = fs.createReadStream(file.path);
  formData.append("media", fileStream, {
    filename: name,
    name: "media",
    contentType: "application/octet-stream",
  });
  let response = await axios.post(
    "https://qyapi.weixin.qq.com/cgi-bin/media/upload",
    formData,
    {
      params: {
        type: sendType,
        access_token: accessToken,
      },
    }
  );
  console.log(response.data);
  if (response.data.errcode === 0) {
    return {
      success: true,
      media_id: response.data.media_id,
      message: "获取media_id成功",
    };
  } else {
    return {
      success: false,
      errcode: response.data.errcode,
      message: response.data.errmsg,
    };
  }
};

exports.uploadTempImage = async (content) => {
  const { sendType, corpId, name, file } = content;
  const accessToken = await token.getToken({ corpId });
  const fileStream = fs.createReadStream(file.path);
  let formData = new FormData();
  formData.append("media", fileStream, {
    filename: name,
    name: "fieldNameHere",
    contentType: "image/png",
  });
  let response = await axios.post(
    "https://qyapi.weixin.qq.com/cgi-bin/media/uploadimg",
    formData,
    {
      params: {
        access_token: accessToken,
      },
    }
  );
  if (response.data.errcode === 0) {
    return {
      success: true,
      url: response.data.url,
      message: "获取rul成功",
      errcode: 0,
    };
  } else {
    return {
      success: false,
      message: "获取url失败",
      errcode: response.data.errcode,
      errmsg: response.data.errmsg,
    };
  }
};
