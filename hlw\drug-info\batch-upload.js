const { getDatabase } = require("../../mongodb");
const { getDrug<PERSON>ist, getDrugFromChineseKeyObj } = require('./verify');


async function getBatchInsertDrugFn() {
  const db = await getDatabase('Internet-hospital');
  if (!db) {
    throw new Error("数据库连接失败");
  }
  const drugCollection = db.collection("drug-info");
  const batch = [];
  let count = 0;
  const list = []
  async function getCount() {
    await Promise.all(list);
    return count;
  }
  async function batchInsertDrug(row, extraInfo) {
    const rowData = {}
    for (const key of Object.keys(row)) {
      rowData[key.trim()]= row[key]
    }
    const [drugData] = getDrugList([rowData], true, extraInfo);
    const { msgs, ...drug } = drugData;
    if (msgs.length === 0) {
      batch.push(drug);
    }
    if (batch.length === 1000) {
      const insert = drugCollection.insertMany(batch.splice(0, 1000));
      list.push(insert);
      const res = await insert;
      count += res.insertedCount;
    }
  }
  async function flushBatch() {
    if (batch.length > 0) {
      const insert = drugCollection.insertMany(batch.splice(0, 1000));
      list.push(insert);
      const res = await insert;
      count += res.insertedCount;
      batch.length = 0; // 清空批次
    }
  }
  return { batchInsertDrug, getCount, flushBatch }
}

module.exports = {
  getBatchInsertDrugFn
}