const services = require('./services');
const config = require('./config');
const logger = require('../utils/logger');

/**
 * 浙江省互联网医院监管平台对接模块
 */
exports.useZyRegulatory = (app) => {

    /**
     * 在线咨询数据上报
     * POST /zy-regulatory/consult
     */
    app.post('/zy-regulatory/consult', async (req, res) => {
        try {
            const result = await services.uploadConsultData(req.body);
            logger.info('在线咨询数据上报成功', { count: result.count });
            res.json({
                success: true,
                data: result,
                message: '在线咨询数据上报成功'
            });
        } catch (error) {
            logger.error('在线咨询数据上报失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '在线咨询数据上报失败'
            });
        }
    });

    /**
     * 在线复诊数据上报
     * POST /zy-regulatory/referral
     */
    app.post('/zy-regulatory/referral', async (req, res) => {
        try {
            const result = await services.uploadReferralData(req.body);
            logger.info('在线复诊数据上报成功', { count: result.count });
            res.json({
                success: true,
                data: result,
                message: '在线复诊数据上报成功'
            });
        } catch (error) {
            logger.error('在线复诊数据上报失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '在线复诊数据上报失败'
            });
        }
    });

    /**
     * 在线处方数据上报
     * POST /zy-regulatory/recipe
     */
    app.post('/zy-regulatory/recipe', async (req, res) => {
        try {
            const result = await services.uploadRecipeData(req.body);
            logger.info('在线处方数据上报成功', { count: result.count });
            res.json({
                success: true,
                data: result,
                message: '在线处方数据上报成功'
            });
        } catch (error) {
            logger.error('在线处方数据上报失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '在线处方数据上报失败'
            });
        }
    });

    /**
     * 健康检查
     * GET /zy-regulatory/health
     */
    app.get('/zy-regulatory/health', (req, res) => {
        res.json({
            success: true,
            data: {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                apiUrl: config.apiUrl
            },
            message: '浙江省互联网医院监管平台对接服务运行正常'
        });
    });

    logger.info('浙江省互联网医院监管平台对接模块已加载');
};
