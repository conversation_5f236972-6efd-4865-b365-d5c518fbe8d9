const dayjs = require("dayjs");
const points = require("./points");
const common = require("../../common");
const api = require("../../api");
let db = null;

exports.main = async (event, DB) => {
  db = DB;
  switch (event.type) {
    case "addServiceRecord":
      return await this.addServiceRecord(event);
    case "updateServiceRecord":
      return await this.updateServiceRecord(event);
    case "getServiceRecord":
      return await this.getServiceRecord(event);
    case "getCustomerServiceRecord":
      return await this.getCustomerServiceRecord(event);
    case "removeServiceRecord":
      return await this.removeServiceRecord(event);
    case "getServiceRecordCountToday":
      return await this.getServiceRecordCountToday(event);
    case "getTodayServiceCustomerCount":
      return await this.getTodayServiceCustomerCount(event);
    case "getLastServiceTime":
      return await this.getLastServiceTime(event);
    case "getCustomerDayServiceCount":
      return await this.getCustomerDayServiceCount(event);
    case "updateCustomerDayServiceCount":
      return await this.updateCustomerDayServiceCount(event);
    case "getCorpServiceRecordCount":
      return await getCorpServiceRecordCount(event);
    case "customerTransferRecord":
      return await customerTransferRecord(event);
    case "getCorpServicePointsList":
    case "getServicePointsStatistics":
      return await points.main(event, db);
    case "getServiceRecordCount":
      return getServiceRecordCount(event);
  }
};

/**
 * 获取公司服务记录数量
 * @param {*} item
 * @returns
 */
async function getCorpServiceRecordCount(item) {
  const { corpId } = item;
  const startOfTodayTimestamp = dayjs().startOf("day").valueOf();
  const endOfTodayTimestamp = dayjs().endOf("day").valueOf();

  // 获取总的服务记录
  const total = await db
    .collection("service-record")
    .find({
      corpId,
      eventType: { $ne: "serviceRate" },
    })
    .count();

  // 获取今日服务记录
  const todayCount = await db
    .collection("service-record")
    .find({
      corpId,
      eventType: { $ne: "serviceRate" },
      createTime: { $gte: startOfTodayTimestamp, $lte: endOfTodayTimestamp },
    })
    .count();

  return {
    success: true,
    message: "获取成功",
    total,
    todayCount,
  };
}

exports.addServiceRecord = async (item, type = "") => {
  const {
    taskContent,
    corpId,
    executorUserId,
    customerId,
    creatorUserId,
    executeTeamId,
    eventType,
    customerName,
    pannedEventSendFile = "",
    executionTime = "",
    externalUserId,
    pannedEventName,
    planId = "",
    result,
    serviceRate,
    transferToTeamIds,
    adminRemoveTeams,
    addMethod,
  } = item;

  let params = {
    _id: common.generateRandomString(24),
    executeTeamId,
    creatorUserId,
    corpId,
    customerName,
    executorUserId,
    customerId,
    eventType,
    createTime: new Date().getTime(),
    taskContent,
    pannedEventSendFile,
    executionTime,
    pannedEventName,
    result,
    customerUserId: externalUserId,
    transferToTeamIds,
    adminRemoveTeams,
    addMethod,
    serviceRate,
  };

  if (type === "ToDoEvent") {
    let title =
      eventType === "followUp"
        ? "已完成就诊"
        : pannedEventName
          ? pannedEventName
          : "";
    if (planId) title = taskContent;
    params["taskContent"] = ` ${title ? "【" + title + "】" : ""}${result ? `处理结果: ${result}` : ""
      }`;
    params["executionTime"] = new Date().getTime();
  }

  try {
    const { points, pointTaskId } = await getServicePoint({
      corpId,
      executorUserId,
      eventType,
      serviceRate,
    });
    if (points) params["points"] = points;
    if (pointTaskId) params["pointTaskId"] = pointTaskId;

    const addTask = db.collection("service-record").insertOne(params);
    const countTask = exports.addServiceDayCount({
      corpId,
      teamId: executeTeamId,
      userId: executorUserId,
      customerId,
      customerName,
      executionTime,
    });
    const updateTask = updateServiceDate({
      id: customerId,
      serviceTime: params.executionTime,
      corpId,
    });

    let array =
      eventType === "serviceRate"
        ? [addTask]
        : [addTask, countTask, updateTask];
    await Promise.all(array);

    return {
      success: true,
      message: "新增成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "新增失败",
    };
  }
};

// 获取积分情况
async function getServicePoint({
  corpId,
  executorUserId,
  eventType,
  serviceRate,
}) {
  if (!executorUserId || !eventType || !corpId) return false;

  let query = { corpId, serviceType: eventType, taskStatus: "enable" };
  if (serviceRate) {
    query["serviceRate"] = serviceRate;
  }

  let list = await db.collection("points-task").find(query).toArray();
  if (!list || list.length === 0) return false;

  const {
    taskTriggerTotal = 0,
    userTriggerlimitTotal = 0,
    userTriggerTodayLimitCount = 0,
    _id: pointTaskId,
    points,
  } = list[0];

  if (typeof points !== "number" || points === 0) return false;

  let queryServiceRecord = {
    corpId,
    eventType,
    points: { $exists: true },
    pointTaskId,
  };
  if (serviceRate) queryServiceRecord["serviceRate"] = serviceRate;

  if (userTriggerlimitTotal > 0) {
    const count = await db
      .collection("service-record")
      .find({ ...queryServiceRecord, executorUserId })
      .count();
    if (count >= userTriggerlimitTotal) return false;
  }

  if (userTriggerTodayLimitCount > 0) {
    const count = await db
      .collection("service-record")
      .find({
        ...queryServiceRecord,
        createTime: {
          $gte: dayjs().startOf("day").valueOf(),
          $lte: dayjs().endOf("day").valueOf(),
        },
        executorUserId,
      })
      .count();
    if (count >= userTriggerTodayLimitCount) return false;
  }

  if (taskTriggerTotal > 0) {
    const count = await db
      .collection("service-record")
      .find(queryServiceRecord)
      .count();
    if (count >= taskTriggerTotal) return false;
  }

  return {
    points,
    pointTaskId,
    serviceRate,
  };
}

// 更新服务时间
async function updateServiceDate({ id, serviceTime, corpId }) {
  if (id && serviceTime && corpId && dayjs(serviceTime).isValid()) {
    const res = await api.getMemberApi({
      type: "updateCustomerTimeField",
      _id: id,
      corpId,
      field: "serviceTime",
      time: dayjs(serviceTime).valueOf(),
    });
    return res;
  }
}

// 新增服务记录, 已完成的待办放在服务记录中
exports.updateServiceRecord = async (item) => {
  const { id, params } = item;
  params["updateTime"] = new Date().getTime();
  try {
    await db
      .collection("service-record")
      .updateOne({ _id: id }, { $set: params });
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
    };
  }
};

exports.getServiceRecordCountToday = async (item) => {
  const { corpId, userId, teamId, customerId } = item;
  const startOfTodayTimestamp = dayjs().startOf("day").valueOf();
  const endOfTodayTimestamp = dayjs().endOf("day").valueOf();

  try {
    const query = {
      corpId,
      executorUserId: userId,
      createTime: { $gte: startOfTodayTimestamp, $lte: endOfTodayTimestamp },
      eventType: { $ne: "serviceRate" },
    };
    if (teamId) query.executeTeamId = teamId;
    if (customerId) query.customerId = customerId;

    const count = await db.collection("service-record").find(query).count();
    return {
      success: true,
      message: "获取成功",
      data: count,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

async function getTeamList(executorUserId, corpId) {
  const result = await api.getCorpApi({
    type: "getTeamBymember",
    corpUserId: executorUserId,
    corpId,
  });
  if (result && Array.isArray(result.data) && result.data.length > 0)
    return result.data.map((item) => item.teamId);
  else return [];
}

// 获取服务记录
exports.getServiceRecord = async (item) => {
  console.log("item", item);
  let { pageSize, params, page, queryType, teamId } = item;
  const { executorUserId, executionTime, corpId, createTime, eventType } = params;

  // 处理 executionTime 参数
  if (typeof executionTime === "string" && executionTime) {
    const startTime = new Date(executionTime).setHours(0, 0, 0, 0);
    const lastTime = new Date(executionTime).setHours(23, 59, 59, 999);
    params["executionTime"] = { $gte: startTime, $lte: lastTime };
  } else if (Array.isArray(executionTime) && executionTime.length) {
    const [start, end] = executionTime.filter((i) => i && dayjs(i).isValid());
    const arr = [];
    if (start) {
      arr.push({ $gte: dayjs(start).startOf("day").valueOf() });
    }
    if (end) {
      arr.push({ $lte: dayjs(end).endOf("day").valueOf() });
    }
    arr.length && (params["executionTime"] = { $and: arr });
  }

  // 处理 createTime 参数
  if (Array.isArray(createTime) && createTime.length) {
    const startDate = createTime[0];
    const endDate = createTime[1] || startDate;
    const startTime = dayjs(startDate).startOf("day").valueOf();
    const lastTime = dayjs(endDate).endOf("day").valueOf();
    params["createTime"] = { $gte: startTime, $lte: lastTime };
  }

  // 处理 queryType 参数
  if (queryType === "team") {
    if (!teamId) {
      const teamIds = await getTeamList(executorUserId, corpId);
      if (teamIds.length > 0) params["executeTeamId"] = { $in: teamIds };
    }
    delete params.executorUserId;
  }

  if (teamId) params["executeTeamId"] = teamId;
  params["eventType"] = eventType ? eventType : { $ne: "serviceRate" };

  console.log("params", params);

  // 获取总数
  const total = await db.collection("service-record").find(params).count();
  const pages = Math.ceil(total / pageSize);

  try {
    // 获取数据
    const data = await db
      .collection("service-record")
      .aggregate([
        { $match: params },
        { $sort: { executionTime: -1 } },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $lookup: {
            from: "team",
            localField: "executeTeamId",
            foreignField: "teamId",
            as: "team",
          },
        },
        {
          $addFields: {
            teamName: { $arrayElemAt: ["$team.name", 0] },
          },
        },
        {
          $project: {
            team: 0,
          },
        },
      ])
      .toArray();

    return {
      success: true,
      message: "获取成功",
      total: total,
      pages: pages,
      size: pageSize,
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.getLastServiceTime = async (context) => {
  const { userIds = [], customerId } = context;
  if (!customerId) return { success: false, message: "客户id不能为空" };

  try {
    const query = { customerId };
    if (Array.isArray(userIds) && userIds.length) {
      query.executorUserId = { $in: userIds };
    }

    const res = await db
      .collection("service-record")
      .aggregate([
        { $match: query },
        {
          $group: {
            _id: "$executorUserId",
            executionTime: { $max: "$executionTime" },
          },
        },
        {
          $project: {
            _id: 0,
            id: "$_id",
            executionTime: 1,
          },
        },
      ])
      .toArray();

    return { success: true, message: "获取成功", data: res };
  } catch (e) {
    return { success: false, message: "获取失败" };
  }
};

// 删除服务记录
exports.removeServiceRecord = async (context) => {
  const { id } = context;
  try {
    await db.collection("service-record").deleteOne({ _id: id });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除失败",
    };
  }
};

exports.getTodayServiceCustomerCount = async (item) => {
  const {
    corpId,
    userId: executorUserId,
    teamId: executeTeamId,
    customerId,
  } = item;
  if (!corpId || !executorUserId || !executeTeamId || !customerId)
    return { success: false, message: "参数错误" };

  try {
    const query = {
      corpId,
      customerId,
      executeTeamId,
      executorUserId,
      executionTime: {
        $gte: dayjs().startOf("day").valueOf(),
        $lt: dayjs().endOf("day").valueOf(),
      },
      eventType: { $ne: "serviceRate" },
    };

    const count = await db.collection("service-record").find(query).count();
    return {
      success: true,
      message: "获取成功",
      data: count,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

exports.getCustomerServiceRecord = async (context) => {
  const {
    corpId,
    userId: executorUserId,
    teamId: executeTeamId,
    customerId,
    executionTime,
  } = context;
  if (
    !corpId ||
    !executorUserId ||
    !executeTeamId ||
    !customerId ||
    !executionTime ||
    !dayjs(executionTime).isValid()
  )
    return { success: false, message: "参数错误" };

  try {
    const query = {
      corpId,
      customerId,
      executeTeamId,
      executorUserId,
      executionTime: {
        $gte: dayjs(executionTime).startOf("day").valueOf(),
        $lt: dayjs(executionTime).endOf("day").valueOf(),
      },
      eventType: { $ne: "serviceRate" },
    };

    const data = await db.collection("service-record").find(query).toArray();
    return {
      success: true,
      message: "获取成功",
      data: data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.addServiceDayCount = async (context) => {
  const { corpId, teamId, userId, customerId, customerName, executionTime } =
    context;
  if (
    !corpId ||
    !teamId ||
    !userId ||
    !customerId ||
    !customerName ||
    !executionTime ||
    !dayjs(executionTime).isValid()
  )
    return { success: false, message: "参数错误" };

  try {
    const date = dayjs(executionTime).startOf("day");
    const query = { corpId, userId, teamId, customerId, date: date.valueOf() };

    const record = await db.collection("service_day_count").findOne(query);
    if (record) {
      await db
        .collection("service_day_count")
        .updateOne({ _id: record._id }, { $inc: { count: 1 } });
    } else {
      await db.collection("service_day_count").insertOne({
        _id: common.generateRandomString(24),
        corpId,
        userId,
        teamId,
        customerId,
        customerName,
        createTime: Date.now(),
        date: date.valueOf(),
        dateStr: date.format("YYYY-MM-DD"),
        rateExpireTime: date.add(1, "day").valueOf(),
        rateStatus: "init",
        count: 1,
      });
    }

    return { success: true, message: "更新成功" };
  } catch (e) {
    return { success: false, message: e.message || "获取失败" };
  }
};

exports.getCustomerDayServiceCount = async (context) => {
  let {
    corpId,
    teamId,
    userId,
    date,
    rateStatus,
    page = 1,
    pageSize = 10,
    customerName,
  } = context;
  if (!corpId || !teamId || !userId)
    return { success: false, message: "参数错误" };

  try {
    const query = { corpId, userId, teamId };
    if (date && dayjs(date).isValid())
      query.date = dayjs(date).startOf("day").valueOf();
    if (typeof customerName === "string" && customerName.trim())
      query.customerName = { $regex: customerName.trim() };
    if (rateStatus === "rated") query.rateStatus = "rated";
    else if (rateStatus === "init") {
      query.rateStatus = "init";
      query.rateExpireTime = { $gte: Date.now() };
    } else if (rateStatus === "expired") {
      query.rateStatus = "init";
      query.rateExpireTime = { $lt: Date.now() };
    }

    const list = await db
      .collection("service_day_count")
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    const total = await db.collection("service_day_count").find(query).count();

    const customerIds = list.map((i) => i.customerId);
    const minTime = Math.min(
      ...list.map((i) => i.date).filter((i) => typeof i === "number")
    );
    const maxTime = Math.max(
      ...list.map((i) => i.rateExpireTime).filter((i) => typeof i === "number")
    );

    let rateList = [];
    if (customerIds.length) {
      const result = await api.getKnowledgeBaseApi({
        type: "getMemberRateRecord",
        corpId,
        customerIds,
        userId,
        teamId,
        minTime,
        maxTime,
      });
      rateList = Array.isArray(result.rateList) ? result.rateList : [];
    }

    return {
      success: true,
      message: "获取成功",
      list,
      total,
      pages: Math.ceil(total / pageSize),
      rateList,
    };
  } catch (e) {
    return { success: false, message: e.message || "获取失败" };
  }
};

exports.updateCustomerDayServiceCount = async (context) => {
  let { corpId, teamId, userId, dateStr, customerId } = context;
  try {
    if (!corpId || !teamId || !userId || !dateStr || !customerId)
      return { success: false, message: "参数错误" };

    await db
      .collection("service_day_count")
      .updateOne(
        { corpId, teamId, userId, dateStr, customerId },
        { $set: { rateStatus: "rated" } }
      );

    await addServiceRateRecord(context);
    return { success: true, message: "更新成功" };
  } catch (e) {
    return { success: false, message: e.message || "更新失败" };
  }
};

async function addServiceRateRecord(context) {
  const {
    corpId,
    teamId,
    userId,
    customerId,
    rate,
    externalUserId,
    customerName,
  } = context;
  const query = {
    _id: common.generateRandomString(24),
    taskContent: "服务评价",
    corpId,
    executorUserId: userId,
    customerId: customerId,
    executeTeamId: teamId,
    eventType: "serviceRate",
    customerName,
    executionTime: new Date().getTime(),
    externalUserId,
    serviceRate: rate,
    result: "完成服务评价",
  };
  await exports.addServiceRecord(query);
}

async function customerTransferRecord(params) {
  const { corpId, customerId } = params;
  if (!corpId || !customerId) return { success: false, message: "参数错误" };

  const data = await db
    .collection("service-record")
    .find({
      corpId,
      customerId,
      eventType: {
        $in: [
          "remindFiling",
          "transferToSameTeam",
          "transferToOtherTeam",
          "transferToCustomerPool",
          "adminAllocateTeams",
          "adminRemoveTeams",
          "share",
        ],
      },
    })
    .sort({ createTime: -1 })
    .toArray();

  return {
    success: true,
    message: "获取成功",
    list: data,
  };
}
