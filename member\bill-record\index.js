const dayjs = require("dayjs");
const treatmentRecord = require("../treatment-record");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getBillRecord":
      return getBillRecord(content);
    case "addBillRecord":
      return addBillRecord(content);
    case "updateBillRecord":
      return updateBillRecord(content);
    case "deductPorjectUsageCount":
      return deductPorjectUsageCount(content);
  }
};

/**
 * 新增账单记录
 * @param {*} content
 * @returns
 */
async function addBillRecord(content) {
  const { corpId, params } = content;
  const { hasValidDays, validDays } = params;
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  if (hasValidDays && validDays)
    params.validTime = dayjs().add(validDays, "day").valueOf();
  const createTime = dayjs().valueOf();
  try {
    const id = common.generateRandomString(24);
    await db.collection("bill-record").insertOne({
      _id: id,
      ...params,
      corpId,
      createTime,
    });
    // 新增治疗记录, 治疗状态为init
    if (params.createTreatmentOrder) {
      await treatmentRecord.main(
        {
          type: "addTreatmentRecord",
          corpId,
          params: {
            ...params,
            billId: id,
            createTreatementTime: dayjs().valueOf(),
            billdCreateTime: createTime,
            treatmentStatus: "init",
          },
        },
        db
      );
    }
    return {
      success: true,
      message: "新增成功",
      data: id,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
}

/**
 * 获取账单记录
 * @param {string} corpId 机构id
 * @returns
 */
async function getBillRecord(content) {
  const {
    corpId,
    customerId,
    haveValid,
    page = 1,
    pageSize = 100,
    billType,
    billDates,
    projects,
    packageList,
    treatmentDept_ids,
    projectType,
    restUsage,
    treatmentStatus,
    consultId,
  } = content;
  let query = {
    corpId,
  };
  if (haveValid) {
    query.validTime = {
      $or: [{ $exists: false }, { $gte: dayjs().endOf("day").valueOf() }],
    };
  }
  if (billType) query["packageName"] = { $exists: billType === "package" };
  if (billDates && Array.isArray(billDates) && billDates.length) {
    query.createTime = {
      $and: [
        { $gte: dayjs(billDates[0]).startOf("day").valueOf() },
        { $lte: dayjs(billDates[1]).endOf("day").valueOf() },
      ],
    };
  }
  if(customerId) query.customerId = customerId;
  if (projects && Array.isArray(projects) && projects.length) {
    query.projectId = { $in: projects };
  }
  if (packageList && Array.isArray(packageList) && packageList.length) {
    query.packageId = { $in: packageList };
  }
  if (
    treatmentDept_ids &&
    Array.isArray(treatmentDept_ids) &&
    treatmentDept_ids.length
  ) {
    query.treatmentDept_id = { $in: treatmentDept_ids };
  }
  if (consultId) {
    query.consultId = consultId;
  }
  if (restUsage) query.restUsageCount = { $gt: 0 };
  if (projectType) query.projectType = projectType;

  const total = await db.collection("bill-record").countDocuments(query);
  const pages = Math.ceil(total / pageSize);
  const billCollection = db
    .collection("bill-record")
    .find(query)
    .sort({ createTime: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);

  let res = [];
  if (treatmentStatus === "pending") {
    res = await billCollection.toArray();
    res = await Promise.all(
      res.map(async (bill) => {
        const treatmentRecords = await db
          .collection("treatment-record")
          .find({ billId: bill._id })
          .toArray();
        bill.treatmentStatus = treatmentRecords.some(
          (record) => record.treatmentStatus === "pending"
        );
        return bill;
      })
    );
  } else {
    res = await billCollection.toArray();
  }

  return {
    list: res,
    success: true,
    message: "获取成功",
    total: total,
    pages: pages,
    size: pageSize,
  };
}

/**
 * 更新账单记录
 * @param {*} content
 * @returns
 */
async function updateBillRecord(content) {
  const { corpId, id, params } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id || !params) return { success: false, message: "缺少必要的字段" };
  try {
    await db
      .collection("bill-record")
      .updateOne({ _id: id, corpId }, { $set: params });
    return { success: true, message: "更新成功" };
  } catch (error) {
    return { success: false, message: "更新失败" };
  }
}

/**
 * 扣减项目使用次数
 * @param {*} content
 * @returns
 */
async function deductPorjectUsageCount({ corpId, billId, deductUsageCount }) {
  await db
    .collection("bill-record")
    .updateOne(
      { _id: billId, corpId },
      { $inc: { restUsageCount: -deductUsageCount } }
    );
  return {
    success: true,
    message: "更新成功",
  };
}
