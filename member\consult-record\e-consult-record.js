const dayjs = require("dayjs");
const common = require("../../common");
const api = require("../../api");

let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "addEConsuleRecord":
      return addEConsuleRecord(content);
    case "updateEConsuleRecord":
      return updateEConsuleRecord(content);
    case "getEConsuleRecord":
      return getEConsuleRecord(content);
    case "getFirstEConsuleRecord":
      return getFirstEConsuleRecord(content);
  }
};

// 新增网络咨询记录
async function addEConsuleRecord(params) {
  const data = verfiyParams(params);
  if (typeof data === "string") return { success: false, message: data };
  data._id = common.generateRandomString(24); // 生成随机字符串作为_id
  data.corpId = params.corpId;
  data.userId = params.userId;
  data.customerId = params.customerId;
  try {
    const res = await db.collection("e-consult-record").insertOne(data);
    return { success: true, message: "新增网络咨询记录成功", data: res.insertedId };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 获取网络咨询记录
async function getEConsuleRecord(params) {
  const {
    page,
    pageSize,
    customerId,
    corpId,
    projectIds,
    startDate,
    endDate,
    source,
  } = params;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const query = { corpId };
  if (customerId) query.customerId = customerId;
  if (Array.isArray(projectIds)) query["projects._id"] = { $in: projectIds };
  if (Array.isArray(source)) query.source = { $in: source };
  const dates = [];
  if (startDate && dayjs(startDate).isValid())
    dates.push({ $gte: dayjs(startDate).startOf("day").valueOf() });
  if (endDate && dayjs(endDate).isValid())
    dates.push({ $lte: dayjs(endDate).endOf("day").valueOf() });
  if (dates.length) query.timestamp = { $and: dates };
  try {
    const list = await db
      .collection("e-consult-record")
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .sort({ timestamp: -1, createTime: -1 })
      .toArray();
    const total = await db.collection("e-consult-record").countDocuments(query);
    const projectIds = [];
    list.forEach((item) => {
      item.projectIds = Array.isArray(item.projects) ? item.projects.map((project) => project && project._id ? project._id : "").filter(Boolean) : [];
      projectIds.push(...item.projectIds);
    })
    if (projectIds.length) {
      const res = await api.getCorpApi({ type: 'getProjectNames', corpId, ids: projectIds })
      const m = new Map()
      const projects = res && Array.isArray(res.data) ? res.data : [];
      projects.forEach(item => {
        if (item._id && item.projectName) {
          m.set(item._id, item.projectName)
        }
      })
      list.forEach(item => {
        item.projectNames = item.projectIds.map(id => m.get(id)).filter(Boolean)
      })
    }
    return {
      success: true,
      list,
      total,
      pages: Math.ceil(total / pageSize),
      message: "查询成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 更新网络咨询记录
async function updateEConsuleRecord(params) {
  const data = verfiyParams(params, true);
  if (typeof data === "string") return { success: false, message: data };
  try {
    const res = await db
      .collection("e-consult-record")
      .updateOne(
        {
          corpId: params.corpId,
          customerId: params.customerId,
          _id: params._id,
        },
        { $set: data }
      );
    if (res.matchedCount == 0)
      return { success: false, message: "更新网络咨询记录失败" };
    return { success: true, message: "更新网络咨询记录成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 获取第一条网络咨询记录
async function getFirstEConsuleRecord(ctx) {
  const { corpId, customerId } = ctx;
  if (!corpId || !customerId) return { success: false, message: "参数错误" };
  try {
    const record = await db
      .collection("e-consult-record")
      .find({ corpId, customerId })
      .sort({ timestamp: 1, createTime: 1 })
      .project({ createTime: 1, userId: 1 })
      .limit(1)
      .toArray();
    if (record.length) return { success: true, record: record[0], message: "查询成功" };
    return { success: false, message: "未查询到网络咨询记录" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 验证参数
function verfiyParams(params, requiredId) {
  if (!params.corpId) return "机构id不能为空";
  if (!requiredId && !params.userId) return "登记人不能为空";
  if (requiredId && !params._id) return "记录id不能为空";
  if (typeof params.customerId != "string" || params.customerId.trim() === "")
    return "客户id不能为空";
  if (!Array.isArray(params.source) || params.source.length === 0)
    return "信息来源不能为空";
  const projects = Array.isArray(params.projects)
    ? params.projects
      .map((i) => ({ projectCateId: i.projectCateId, _id: i._id }))
      .filter((i) => i.projectCateId && i._id)
    : [];
  if (projects.length === 0) return "项目不能为空";
  if (!params.date || !dayjs(params.date).isValid()) return "日期不能为空";
  if (dayjs().isBefore(dayjs(params.date))) return "日期不能大于当前日期";
  const data = {
    source: params.source,
    projects,
    date: dayjs(params.date).format("YYYY-MM-DD HH:mm"),
    timestamp: dayjs(params.date).valueOf(),
    reportDesc:
      typeof params.reportDesc === "string" ? params.reportDesc.trim() : "",
  };
  if (requiredId) data.updateTime = Date.now();
  else data.createTime = Date.now();
  return data;
}
