/**
 * 浙江省互联网医院监管平台对接配置
 */

const config = {
    // 应用凭证配置
    appId: 'ngari61b7fb59e6fe3a32',
    appSecret: 'e6fe3a328b2b2351',
    aesKey: '61b7fb590fbb0157', // 16位AES-128加密密钥

    // 固定机构信息配置
    organInfo: {
        organID: '330602PDY78397419A2102',        // 全国统一的组织机构代码
        unitID: '5BE610B1-E9F3-46E1-BE3E 7EC7A931E23F',                   // 监管平台机构ID
        organName: '绍兴市同源健康管理有限公司越城震元堂中医院'          // 机构名称
    },

    // API地址配置
    apiUrl: process.env.ZY_REGULATORY_NETWORK_TYPE === 'health' 
        ? 'https://*********:28212/province/supervise/data'  // 卫生专网
        : 'https://*************:28212/province/supervise/data', // 政务云
    
    // 服务配置
    services: {
        consult: 'uploadConsultIndicators',      // 在线咨询
        referral: 'uploadReferralIndicators',    // 在线复诊  
        recipe: 'uploadRecipeIndicators'         // 在线处方
    },
    
    // 请求配置
    timeout: 30000,
    retryTimes: 1, // 减少重试次数，避免多次请求
    retryDelay: 2000
};

module.exports = config;
