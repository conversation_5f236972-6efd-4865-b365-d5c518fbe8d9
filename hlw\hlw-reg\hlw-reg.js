const common = require("../../common");
const zytHis = require("../zyt-his");
const { ObjectId } = require("mongodb");

const mdtrtCertTypes = ['03']

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "hlwWebReg":
      return await hlwWebReg(item);
    case "hlwWebRegSettle":
      return await hlwWebRegSettle(item);
    case "getPatientHlwRegList":
      return await getPatientHlwRegList(item);
    case 'hlwRegRefund':
      return await hlwRegRefund(item);
    case "getRegPayStatus":
      return await getRegPayStatus(item);
  }
};

async function hlwWebReg(ctx) {
  const { name, patientId, doctorNo, deptCode, feeCode, storeId, mdtrtCertType, mdtrtCertNo, cardSn } = ctx;
  if (typeof name !== 'string' || name.trim() === '') {
    return { success: false, message: "姓名不能为空" }
  }
  if (typeof patientId !== 'string' || patientId.trim() === '') {
    return { success: false, message: "患者id不能为空" }
  }
  if (typeof doctorNo !== 'string' || doctorNo.trim() === '') {
    return { success: false, message: "医生编号不能为空" }
  }
  if (typeof deptCode !== 'string' || deptCode.trim() === '') {
    return { success: false, message: "科室编号不能为空" }
  }
  if (typeof storeId !== 'string' || storeId.trim() === '') {
    return { success: false, message: "门店id不能为空" }
  }
  if (typeof feeCode !== 'string' || feeCode.trim() === '') {
    return { success: false, message: "收费编码不能为空" }
  }
  if (!mdtrtCertTypes.includes(mdtrtCertType)) {
    return { success: false, message: "无效的就诊凭证类型" }
  }
  if (typeof mdtrtCertNo !== 'string' || mdtrtCertNo.trim() === '') {
    return { success: false, message: "就诊凭证号码不能为空" }
  }
  if (mdtrtCertType === '03' && (typeof cardSn !== 'string' || cardSn.trim() === '')) {
    return { success: false, message: "社会保障卡内码不能为空" }
  }
  try {
    //  校验 医生状态 门店状态 收费项目状态
    const doctor = await db.collection("hlw-doctor").findOne({ doctorNo, job: 'doctor' }, { projection: { doctorName: 1, doctorNo: 1, onlineStatus: 1, deptCode: 1, _id: 0, deptName: 1, title: 1 } });
    if (!doctor) return { success: false, message: "医生不存在" };
    if (doctor.deptCode !== deptCode) return { success: false, message: "医生科室不匹配" };
    if (doctor.onlineStatus !== 'online') return { success: false, message: "医生不在线" };
    // 查询门店配置信息
    const [store] = await db.collection("store-list").aggregate([
      { $match: { store_id: storeId } },
      { $lookup: { from: "charge-item-list", localField: "charge_id", foreignField: "code", as: "chargeItem" } },
      { $unwind: { path: '$chargeItem', preserveNullAndEmptyArrays: true } }
    ]).toArray();
    if (!store) return { success: false, message: "门店不存在" };
    if (store.type !== '其他') return { success: false, message: "暂只支持社会门店挂号" }
    if (store.charge_id !== feeCode) return { success: false, message: "门店收费编码不匹配" };
    if (!store.chargeItem) return { success: false, message: "收费项目不存在" };
    const payload = {
      name,
      patientId,
      mdtrtCertType,
      mdtrtCertNo: mdtrtCertNo.trim(),
      doctorCode: doctorNo,
      doctorName: doctor.doctorName,
      deptName: doctor.deptName,
      unitCode: deptCode,
      registerId: `H${common.generateRandomString(15)}`,
      chargecode: store.charge_id,
      reg_fee: store.chargeItem.fee,
      title: doctor.title || '',
      shopid: storeId,
      shopname: store.name,
      createTime: Date.now(),
      appwin: 'win',
      op_id: storeId,
      cardSn: cardSn.trim(),
    }
    const { success, data, message } = await zytHis({ type: "registration", ...payload })
    payload.register = data && data.register ? data.register : {}; // medorg_order_no
    if (!success) return { success, message };
    const { insertedId } = await db.collection("hlw-reg").insertOne(payload);
    // 进行线下医保预结算
    const preSettle = await zytHis({ type: "insurancePreSettle", ...payload, mdtrtCertType, mdtrtCertNo, cardSn, medorg_order_no: data.register.medorg_order_no, operator_code: store.store_id, operator_name: store.name })
    if (!preSettle.success) return { success: false, message: preSettle.message, data: insertedId }
    const { outparm_1101, outparm_2206A, inparm_2207A, outparm_2204, ...preSettleData } = preSettle.data;
    await db.collection("hlw-reg").updateOne({ _id: insertedId }, { $set: { preSettle: preSettleData } });
    return { success, message, data: preSettle.data, id: insertedId }
  } catch (e) {
    return { success: false, message: e.message }
  }
}


async function hlwWebRegSettle(ctx) {
  const { id, orderno, fee, outparm_1101, outparm_2206A, inparm_2207A, outparm_2204 } = ctx;
  if (typeof id !== 'string' || id.trim() === '' || typeof orderno !== 'string' || orderno.trim() === '') {
    return { success: false, message: "缺少必要参数" }
  }
  try {
    const data = await db.collection("hlw-reg").findOne({ _id: new ObjectId(id), registerId: orderno }, { projection: { preSettle: 1, shopid: 1, shopname: 1 } });
    if (!data) return { success: false, message: "未找到该挂号记录" }
    if (!data.preSettle) return { success: false, message: "未进行医保预结算" }
    if (fee !== data.preSettle.selfpay) return { success: false, message: "实收金额与应收金额不符" };
    const res = await zytHis({ type: "insuranceSettle", operator_code: data.shopid, operator_name: data.shopname, ...data.preSettle, outparm_1101, outparm_2206A, inparm_2207A, outparm_2204 })
    await db.collection("hlw-reg").updateOne({ _id: data._id }, { $set: { hisStatus: '0' } })
    return { success: res.success, message: res.message, data: res.data }
  } catch (e) {
    return { success: false, message: e.message || '结算失败' }
  }
}

async function getPatientHlwRegList(ctx) {
  const { patientId, cardNum, storeId, startTime, endTime } = ctx;
  if (typeof patientId !== 'string' || patientId.trim() === '') {
    return { success: false, message: "患者id不能为空" }
  }
  if (typeof cardNum !== 'string' || cardNum.trim() === '') {
    return { success: false, message: "卡号不能为空" }
  }
  if (typeof storeId !== 'string' || storeId.trim() === '') {
    return { success: false, message: "门店id不能为空" }
  }
  try {
    const projection = {
      _id: 1,
      name: 1,
      doctorName: 1,
      deptName: 1,
      reg_fee: 1,
      createTime: 1,
      shopname: 1,
      patientId: 1,
      registerId: 1,
      title: 1,
      hisStatus: 1,
      'preSettle.totalamount': 1,
      'preSettle.selfpay': 1
    }
    const query = { patientId, mdtrtCertNo: cardNum, shopid: storeId, hisStatus: { $exists: true } };
    if (startTime > 0) {
      query.createTime = { $gte: startTime }
    }
    if (endTime > 0) {
      query.createTime = query.createTime || {};
      query.createTime['$lte'] = endTime
    }
    const list = await db.collection("hlw-reg").find(query, { projection }).sort({ createTime: -1 }).toArray();
    return { success: true, message: "查询成功", data: list }
  } catch (e) {
    return { success: false, message: e.message }
  }
}


async function hlwRegRefund(ctx) {
  const { _id, registerId, storeId, mdtrtCertNo, cardSn, corpId } = ctx;
  if (typeof _id !== 'string' || _id.trim() === '' || typeof registerId !== 'string' || registerId.trim() === '') {
    return { success: false, message: "缺少必要参数" }
  }
  try {
    const query = {
      _id: new ObjectId(_id),
      'preSettle.orderno': registerId,
      shopid: typeof storeId === 'string' ? storeId.trim() : '',
      mdtrtCertNo: typeof mdtrtCertNo === 'string' ? mdtrtCertNo.trim() : '',
      cardSn: typeof cardSn === 'string' ? cardSn.trim() : '',
    }
    const record = await db.collection("hlw-reg").findOne(query);
    if (!record) return { success: false, message: "未找到挂号记录" };
    const payload = {
      patientid: record.preSettle.patientid,
      orderno: record.preSettle.orderno,
      mdtrt_cert_type: record.mdtrtCertType,
      mdtrt_cert_no: record.mdtrtCertNo,
      card_sn: record.cardSn,
      operator_code: record.shopid,
      operator_name: record.shopname
    }
    const { success, data, message } = await zytHis({ type: "insuranceRefund", ...payload });
    if (!success) return { success, message }
    await db.collection("hlw-reg").updateOne({ _id: record._id }, { $set: { hisStatus: '1' } })
    const orderApi = require('../consult-order');
    const orderRefundRes = await orderApi({ type: 'customerRefundOrder', registerId: record.registerId, patientId: record.patientId }, db);
    if (orderRefundRes.orderId) {
      const triggerApi = require('../consult-order/trigger');
      triggerApi({ type: 'deleteDelayedTask', triggerTaskId: record.orderId }, db);
      const rxApi = require('../diagnostic-record');
      await rxApi({ type: 'discardDiagnosticRecord', orderId: orderRefundRes.orderId }, db);
    }
    return { success, data, message }

  } catch (e) {
    return { success: false, message: e.message }
  }
}


async function getRegPayStatus(ctx) {
  const { registerId, patientId, id, hisStatus } = ctx;
  if ([registerId, patientId, id, hisStatus].some(i => typeof i !== 'string' || i.trim() === '')) {
    return { success: false, message: "缺少必要参数" }
  }
  try {
    const { status, message, success } = await zytHis({ type: "getPayStatus", patientId, registerId, medorgOrderNo: registerId });
    if (success && status && status !== hisStatus) {
      await db.collection("hlw-reg").updateOne({ _id: new ObjectId(id), registerId, patientId }, { $set: { hisStatus: status } })
    }
    return { status, message, success }
  } catch (e) {
    return { success: false, message: e.message }
  }
}