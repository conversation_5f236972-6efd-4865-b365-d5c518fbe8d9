const subjectDict = {
  "01": "预防保健科",
  "02": "全科医疗科",
  "03": "内科",
  "03.01": "呼吸内科专业",
  "03.02": "消化内科专业",
  "03.03": "神经内科专业",
  "03.04": "心血管内科专业",
  "03.05": "血液内科专业",
  "03.06": "肾病学专业",
  "03.07": "内分泌专业",
  "03.08": "免疫学专业",
  "03.09": "变态反应专业",
  "03.10": "老年病专业",
  "04": "外科",
  "04.01": "普通外科专业",
  "04.02": "神经外科专业",
  "04.03": "骨科专业",
  "04.04": "泌尿外科专业",
  "04.05": "胸外科专业",
  "04.06": "心脏大血管外科专业",
  "04.07": "烧伤科专业",
  "04.08": "整形外科专业",
  "05": "妇产科",
  "05.01": "妇科专业",
  "05.02": "产科专业",
  "05.03": "计划生育专业",
  "05.04": "优生学专业",
  "05.05": "生殖健康与不孕症专业",
  "06": "妇女保健科",
  "06.01": "青春期保健专业",
  "06.02": "围产期保健专业",
  "06.03": "更年期保健专业",
  "06.04": "妇女心理卫生专业",
  "06.05": "妇女营养专业",
  "07": "儿科",
  "07.01": "新生儿专业",
  "07.02": "小儿传染病专业",
  "07.03": "小儿消化专业",
  "07.04": "小儿呼吸专业",
  "07.05": "小儿心脏病专业",
  "07.06": "小儿肾病专业",
  "07.07": "小儿血液病专业",
  "07.08": "小儿神经病学专业",
  "07.09": "小儿内分泌专业",
  "07.10": "小儿遗传病专业",
  "07.11": "小儿免疫专业",
  "08": "小儿外科",
  "08.01": "小儿普通外科专业",
  "08.02": "小儿骨科专业",
  "08.03": "小儿泌尿外科专业",
  "08.04": "小儿胸心外科专业",
  "08.05": "小儿神经外科专业",
  "09": "儿童保健科",
  "09.01": "儿童生长发育专业",
  "09.02": "儿童营养专业",
  "09.03": "儿童心理卫生专业",
  "09.04": "儿童五官保健专业",
  "09.05": "儿童康复专业",
  "10": "眼科",
  "11": "耳鼻咽喉科",
  "11.01": "耳科专业",
  "11.02": "鼻科专业",
  "11.03": "咽喉科专业",
  "12": "口腔科",
  "12.01": "牙体牙髓病专业",
  "12.02": "牙周病专业",
  "12.03": "口腔粘膜病专业",
  "12.04": "儿童口腔专业",
  "12.05": "口腔颌面外科专业",
  "12.06": "口腔修复专业",
  "12.07": "口腔正畸专业",
  "12.08": "口腔种植专业",
  "12.09": "口腔麻醉专业",
  "12.10": "口腔颌面医学影像专业",
  "13": "皮肤科",
  "13.01": "皮肤病专业",
  "13.02": "性传播疾病专业",
  "14": "医疗美容科",
  "14.01": "美容外科",
  "14.02": "美容牙科",
  "14.03": "美容皮肤科",
  "14.04": "美容中医科",
  "14.05": "医疗美容心理诊断及辅导",
  "14.06": "美容医疗应用技术",
  "15": "精神科",
  "15.01": "精神病专业",
  "15.02": "精神卫生专业",
  "15.03": "药物依赖专业",
  "15.04": "精神康复专业",
  "15.05": "社区防治专业",
  "15.06": "临床心理专业",
  "15.07": "司法精神专业",
  "16": "传染科",
  "16.01": "肠道传染病专业",
  "16.02": "呼吸道传染病专业",
  "16.03": "肝炎专业",
  "16.04": "虫媒传染病专业",
  "16.05": "动物源性传染病专业",
  "16.06": "蠕虫病专业",
  "17": "结核病科",
  "18": "地方病科",
  "19": "肿瘤科",
  "20": "急诊医学科",
  "21": "康复医学科",
  "22": "运动医学科",
  "23": "职业病科",
  "23.01": "职业中毒专业",
  "23.02": "尘肺专业",
  "23.03": "放射病专业",
  "23.04": "物理因素损伤专业",
  "23.05": "职业健康监护专业",
  "24": "临终关怀科",
  "25": "特种医学与军事医学科",
  "26": "麻醉科",
  "27": "疼痛科",
  "28": "重症医学科",
  "30": "医学检验科",
  "30.01": "临床体液、血液专业",
  "30.02": "临床微生物学专业",
  "30.03": "临床化学检验专业",
  "30.04": "临床免疫、血清学专业",
  "30.05": "临床细胞分子遗传学专业",
  "31": "病理科",
  "32": "医学影像科",
  "32.01": "X 线诊断专业",
  "32.02": "CT 诊断专业",
  "32.03": "磁共振成像诊断专业",
  "32.04": "核医学专业",
  "32.05": "超声诊断专业",
  "32.06": "心电诊断专业",
  "32.07": "脑电及脑血流图诊断专业",
  "32.08": "神经肌肉电图专业",
  "32.09": "介入放射学专业",
  "32.10": "放射治疗专业",
  "50": "中医科",
  "50.01": "内科专业",
  "50.02": "外科专业",
  "50.03": "妇产科专业",
  "50.04": "儿科专业",
  "50.05": "皮肤科专业",
  "50.06": "眼科专业",
  "50.07": "耳鼻咽喉科专业",
  "50.08": "口腔科专业",
  "50.09": "肿瘤科专业",
  "50.10": "骨伤科专业",
  "50.11": "肛肠科专业",
  "50.12": "老年病科专业",
  "50.13": "针灸科专业",
  "50.14": "推拿科专业",
  "50.15": "康复医学专业",
  "50.16": "急诊科专业",
  "50.17": "预防保健科专业",
  "50.18": "其他",
  "50.19": "传染病科专业",
  "50.20": "重症医学科专业",
  "50.21": "治未病科专业",
  "50.22": "中医学与军事医学科专业",
  "50.23": "神志病科专业",
  "51": "民族医学科",
  "51.01": "维吾尔医学",
  "51.02": "藏医学",
  "51.03": "蒙医学",
  "51.04": "彝医学",
  "51.05": "傣医学",
  "52": "中西医结合科",
  "90": "药学专业",
  "91": "护理专业"
};
// 浙江省行政区划字典，code: 名称
const zhejiangDivisionDict = {
  "33": "浙江省",
  "3301": "杭州市",
  "330101": "市辖区",
  "330102": "上城区",
  "330103": "下城区",
  "330104": "江干区",
  "330105": "拱墅区",
  "330106": "西湖区",
  "330108": "滨江区",
  "330122": "桐庐县",
  "330127": "淳安县",
  "330181": "萧山区",
  "330182": "建德市",
  "330183": "富阳市",
  "330184": "余杭区",
  "330185": "临安区",
  "3302": "宁波市",
  "330201": "市辖区",
  "330203": "海曙区",
  "330204": "江东区",
  "330205": "江北区",
  "330206": "北仑区",
  "330211": "镇海区",
  "330225": "象山县",
  "330226": "宁海县",
  "330227": "鄞县",
  "330281": "余姚市",
  "330282": "慈溪市",
  "330283": "奉化市",
  "3303": "温州市",
  "330301": "市辖区",
  "330302": "鹿城区",
  "330303": "龙湾区",
  "330304": "瓯海区",
  "330322": "洞头县",
  "330324": "永嘉县",
  "330326": "平阳县",
  "330327": "苍南县",
  "330328": "文成县",
  "330329": "泰顺县",
  "330381": "瑞安市",
  "330382": "乐清市",
  "3304": "嘉兴市",
  "330401": "市辖区",
  "330402": "秀城区",
  "330411": "郊区",
  "330421": "嘉善县",
  "330424": "海盐县",
  "330481": "海宁市",
  "330482": "平湖市",
  "330483": "桐乡市",
  "3305": "湖州市",
  "330501": "市辖区",
  "330521": "德清县",
  "330522": "长兴县",
  "330523": "安吉县",
  "3306": "绍兴市",
  "330601": "市辖区",
  "330602": "越城区",
  "330603": "柯桥区",
  "330621": "绍兴县",
  "330624": "新昌县",
  "330681": "诸暨市",
  "330682": "上虞市",
  "330683": "嵊州市",
  "3307": "金华市",
  "330701": "市辖区",
  "330702": "婺城区",
  "330721": "金华县",
  "330723": "武义县",
  "330726": "浦江县",
  "330727": "磐安县",
  "330781": "兰溪市",
  "330782": "义乌市",
  "330783": "东阳市",
  "330784": "永康市",
  "3308": "衢州市",
  "330801": "市辖区",
  "330802": "柯城区",
  "330821": "衢县",
  "330822": "常山县",
  "330824": "开化县",
  "330825": "龙游县",
  "330881": "江山市",
  "3309": "舟山市",
  "330901": "市辖区",
  "330902": "定海区",
  "330903": "普陀区",
  "330921": "岱山县",
  "330922": "嵊泗县",
  "3310": "台州市",
  "331001": "市辖区",
  "331002": "椒江区",
  "331003": "黄岩区",
  "331004": "路桥区",
  "331021": "玉环县",
  "331022": "三门县",
  "331023": "天台县",
  "331024": "仙居县",
  "331081": "温岭市",
  "331082": "临海市",
  "3325": "丽水市",
  "332501": "莲都区",
  "332502": "龙泉市",
  "332522": "青田县",
  "332523": "云和县",
  "332525": "庆元县",
  "332526": "缙云县",
  "332527": "遂昌县",
  "332528": "松阳县",
  "332529": "景宁畲族自治县"
};

// 医生职称字典，code: 职称名称
const doctorTitleDict = {
  "1": "主任医师",
  "2": "副主任医师",
  "3": "主治医师",
  "4": "住院医师",
  "5": "教授",
  "6": "副教授",
  "7": "讲师",
  "8": "主任药师",
  "9": "副主任药师",
  "10": "主管药师",
  "11": "药师",
  "12": "药士",
  "13": "技师",
  "14": "主管技师",
  "15": "副主任技师",
  "16": "主任技师",
  "17": "护士",
  "18": "护师",
  "19": "主管护师",
  "20": "副主任护师",
  "21": "主任护师",
  "22": "主任中医师",
  "23": "副主任中医师",
  "24": "主治中医师",
  "25": "中医师",
  "26": "医师",
  "27": "名中医",
  "28": "国医大师",
  "99": "其他",
  "100": "研究员",
  "101": "副研究员",
  "102": "副主任心理技师",
  "103": "主任心理技师",
  "104": "心理治疗师",
  "105": "催眠治疗师"
};
// 证件类型字典，code: 名称
const certTypeDict = {
  "1": "身份证",
  "2": "护照",
  "3": "军官证",
  "4": "台胞证",
  "5": "回乡证",
  "99": "其他"
};

// 与本人关系字典，code: 名称
const relationDict = {
  "0": "本人",
  "1": "丈夫",
  "2": "妻子",
  "3": "儿子",
  "4": "女儿",
  "5": "父亲",
  "6": "母亲",
  "7": "其他"
};
module.exports = { subjectDict, zhejiangDivisionDict,certTypeDict,relationDict,doctorTitleDict };