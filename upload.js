const multer = require("multer");
const path = require("path");
const express = require("express");
const fs = require("fs");
const api = require("./api");
const { safeSend, sendError } = require("./utils/response");

// 设置存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../uploads");
    // 确保目标目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    // 确定文件存储的目录为项目根目录的 'uploads/corpId' 文件夹
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 给文件添加一个唯一的前缀，以防重名冲突
    const fileName = Buffer.from(file.originalname, "latin1").toString("utf8");
    cb(null, `${Date.now()}-${Math.floor(Math.random() * 100000)}-${fileName}`);
  },
});

// 文件类型过滤（可选）
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = /jpeg|jpg|png|gif|mp4|avi|mov|pdf/;
  // 检查文件扩展名是否匹配
  const extname = allowedTypes.test(
    path.extname(file.originalname).toLowerCase()
  );

  if (extname) {
    return cb(null, true);
  } else {
    // 如果文件类型不符合要求，返回错误
    cb(new Error("只允许上传图片和视频文件"));
  }
};

// 创建multer实例，并应用存储配置和文件过滤
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB 文件大小限制
  }
});

exports.uploadFile = (app) => {
  // 设置静态文件目录
  app.use("/uploads", express.static(path.join(__dirname, "../uploads")));
  
  // 创建上传路由
  app.post("/upload", (req, res) => {
    // 使用 multer 中间件处理文件上传
    upload.single("file")(req, res, async (err) => {
      // 检查是否已经发送响应
      if (res.headersSent) {
        return;
      }

      if (err) {
        console.error("文件上传错误:", err);
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return sendError(res, "文件大小超过限制(最大10MB)", err.message, 400);
          }
          return sendError(res, "文件上传错误", err.message, 400);
        }
        return sendError(res, "文件上传失败", err.message, 500);
      }

      try {
        const { file } = req;
        if (!file) {
          return sendError(res, "没有文件被上传", null, 400);
        }

        const { corpId = "default", getType, sendType, name } = req.body;
        
        if (getType === "image" || getType === "mediaId") {
          const { media_id, success, url } = await getWechatTemp({
            corpId,
            sendType,
            name,
            file,
            getType,
          });
          
          if (!success) {
            return sendError(res, "文件上传失败", null, 500);
          }
          
          // 返回media_id
          return safeSend(res, {
            message: "文件上传成功",
            media_id,
            url,
            success: true,
          });
        }
        
        // 文件存储路径
        const filePath = path.join("/uploads", file.filename);
        const normalizedPath = filePath.replace(/\\/g, "/");
        
        // 返回文件路径和成功消息
        return safeSend(res, {
          message: "文件上传成功",
          filePath: normalizedPath,
          success: true,
        });
        
      } catch (error) {
        console.error("处理上传文件时出错:", error);
        return sendError(res, "文件上传失败", error.message, 500);
      }
    });
  });
};

async function getWechatTemp(item) {
  const { corpId = "", getType, sendType, name, file } = item;
  try {
    // uploadTempImage
    const { media_id, success, url } = await api.getWecomApi({
      type: getType === "image" ? "uploadTempImage" : "uploadTempMedia",
      corpId,
      sendType,
      name,
      file,
    });
    
    if (!success) {
      return {
        message: "文件上传失败",
        success: false,
      };
    }
    
    return {
      message: "文件上传成功",
      media_id,
      url,
      success: true,
    };
  } catch (error) {
    console.error("获取微信临时素材失败:", error);
    return {
      message: "文件上传失败",
      success: false,
      error: error.message,
    };
  }
}
