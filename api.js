const mongodb = require("./mongodb");
const corp = require("./corp");
const knowledgeBase = require("./knowledgeBase");
const member = require("./member");
const todo = require("./toDoEvents");
const weCom = require("./weCom");
const sessionArchive = require("./sessionArchive");
const groupmsg = require("./groupmsg");
const customerHisSync = require("./customerHisSync");
const { getDatabase } = require("./mongodb");
exports.getCorpApi = async (item) => {
  try {
    const db = await getDatabase("corp");
    return await corp.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};
exports.getGroupMsgApi = async (item) => {
  try {
    const db = await getDatabase("admin");
    return await groupmsg.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getSessionArchive = async (item) => {
  try {
    const db = await getDatabase("corp");
    return await sessionArchive.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getKnowledgeBaseApi = async (item) => {
  try {
    const db = await getDatabase("corp");
    return await knowledgeBase.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getMemberApi = async (item) => {
  try {
    const db = await getDatabase("admin");
    return await member.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getTodoApi = async (item) => {
  try {
    const db = await getDatabase("admin");
    return await todo.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getCustomerHisSyncApi = async (item) => {
  try {
    const db = await getDatabase("admin");
    return await customerHisSync.main(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getWecomApi = async (item) => {
  try {
    return await weCom.main(item);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

exports.getInternetHospitalApi = async (item) => {
  try {
    const hlw = require('./hlw/index')
    const db = await getDatabase("Internet-hospital");
    return await hlw(item, db);
  } catch (err) {
    return {
      success: false,
      message: "请求失败!",
      err,
    };
  }
};

