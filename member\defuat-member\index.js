const { MongoClient } = require('mongodb');
const common = require('../../common');

let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "addDefuatMember":
      return await this.addDefuatMember(content);
    case "updateDefautMember":
      return await this.updateDefautMember(content);
    case "getDefaultMember":
      return await this.getDefaultMember(content);
  }
};

// 新增默认就诊人
exports.addDefuatMember = async (context) => {
  try {
    await db.collection("defaultMember").insertOne({
      _id: common.generateRandomString(24),
      unionid: context.unionid,
      defaultMemberId: context.id,
      createTime: new Date().getTime(),
    });
    return {
      success: true,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 更改默认就诊人
exports.updateDefautMember = async (context) => {
  try {
    await db.collection("defaultMember").updateOne(
      { unionid: context.unionid },
      {
        $set: {
          defaultMemberId: context.id,
          updateTime: new Date().getTime(),
        },
      }
    );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 获取默认就诊人Id
exports.getDefaultMember = async (context) => {
  try {
    const res = await db.collection("defaultMember").findOne({
      unionid: context.unionid,
    });
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};
