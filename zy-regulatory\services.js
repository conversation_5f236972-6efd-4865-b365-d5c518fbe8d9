const client = require('./client');
const config = require('./config');
const { subjectDict } = require('./dict');

/**
 * 添加选填字段（只有当字段有值时才添加）
 * @param {object} target 目标对象
 * @param {string} key 字段名
 * @param {any} value 字段值
 * @param {any} defaultValue 默认值（可选）
 */
function addOptionalField(target, key, value, defaultValue = null) {
    // 检查值是否有效（不为空、不为undefined、不为空字符串）
    if (value !== null && value !== undefined && value !== '') {
        target[key] = value;
    } else if (defaultValue !== null && defaultValue !== undefined && defaultValue !== '') {
        target[key] = defaultValue;
    }
    // 如果值无效且没有默认值，则不添加该字段
}

/**
 * 格式化时间为datetime字符串
 * @param {number|string|Date} timestamp 时间戳、日期字符串或Date对象
 * @returns {string} YYYY-MM-DD HH:mm:ss 格式的时间字符串
 */
function formatDateTime(timestamp) {
    if (!timestamp) return null;

    let date;
    if (typeof timestamp === 'number') {
        date = new Date(timestamp);
    } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
    } else if (timestamp instanceof Date) {
        date = timestamp;
    } else {
        return null;
    }

    // 格式化为 YYYY-MM-DD HH:mm:ss
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化日期为date字符串
 * @param {number|string|Date} timestamp 时间戳、日期字符串或Date对象
 * @returns {string} YYYY-MM-DD 格式的日期字符串
 */
function formatDate(timestamp) {
    if (!timestamp) return null;

    let date;
    if (typeof timestamp === 'number') {
        date = new Date(timestamp);
    } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
    } else if (timestamp instanceof Date) {
        date = timestamp;
    } else {
        return null;
    }

    // 格式化为 YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
}

/**
 * 格式化诊断信息
 * @param {Array|string} diagnoses 诊断数据
 * @param {string} type 诊断类型 ('original' | 'current')
 * @param {string} answerFlag 是否回复标志
 * @returns {string} 格式化后的诊断字符串
 */
function formatDiagnosis(diagnoses, type = 'original', answerFlag = '1') {
    // 如果是本次复诊诊断且未回复/拒绝，返回"无"
    if (type === 'current' && answerFlag === '0') {
        return '无';
    }

    if (!diagnoses) {
        return type === 'current' ? '无' : '';
    }

    let diagnosisArray = [];

    if (Array.isArray(diagnoses)) {
        // 处理数组格式的诊断数据
        diagnosisArray = diagnoses.map(item => {
            if (typeof item === 'string') {
                return item;
            } else if (item && item.name) {
                // 处理 diagnosisList 格式: {code, name, desc}
                return item.name;
            }
            return '';
        }).filter(name => name && name.trim() !== '');
    } else if (typeof diagnoses === 'string') {
        // 处理字符串格式
        diagnosisArray = [diagnoses];
    }

    // 如果没有有效诊断
    if (diagnosisArray.length === 0) {
        return type === 'current' ? '无' : '';
    }

    // 使用"|"分隔多个诊断
    return diagnosisArray.join('|');
}

/**
 * 根据科室名称获取专业代码和名称
 * @param {string} deptName 科室名称
 * @returns {object} {subjectCode, subjectName}
 */
function getSubjectByDeptName(deptName) {
    if (!deptName) {
        return { subjectCode: '', subjectName: '' };
    }

    // 遍历字典查找匹配的科室
    for (const [code, name] of Object.entries(subjectDict)) {
        if (name.includes(deptName) || deptName.includes(name)) {
            return { subjectCode: code, subjectName: name };
        }
    }

    // 如果没有找到精确匹配，尝试模糊匹配
    const normalizedDeptName = deptName.replace(/科$/, ''); // 去掉"科"字
    for (const [code, name] of Object.entries(subjectDict)) {
        if (name.includes(normalizedDeptName) || normalizedDeptName.includes(name.replace(/专业$/, ''))) {
            return { subjectCode: code, subjectName: name };
        }
    }

    // 默认返回空值
    return { subjectCode: '', subjectName: '' };
}

/**
 * 在线咨询数据上报
 * @param {object|array} consultData 咨询数据
 * @returns {Promise<object>} 上报结果
 */
async function uploadConsultData(consultData) {
    try {
        const data = Array.isArray(consultData) ? consultData : [consultData];

        // 数据格式化和验证
        const formattedData = data.map(item => {
            // 根据科室名称获取专业代码和名称
            const subjectInfo = getSubjectByDeptName(item.deptName);

            // 构建必填字段
            const result = {
                // ========== 必填字段 ==========
                organID: config.organInfo.organID,                    // 全国统一的组织机构代码
                unitID: config.organInfo.unitID,                      // 监管平台机构ID
                organName: config.organInfo.organName,                // 机构名称
                bussID: item.orderId || '',                           // 咨询记录Id
                subjectCode: item.subjectCode || subjectInfo.subjectCode, // 所属专业代码(从字典获取)
                subjectName: item.subjectName || subjectInfo.subjectName, // 所属专业名称(从字典获取)
                doctorCertID: item.doctorCertID || '330102199001011234',  // 医师身份证号（测试默认值）
                patientCertType: item.patientCertType || '1',        // 患者证件类型
                patientCertID: item.idCard,                     // 患者证件号
                sex: item.sex || '3',                                  // 患者性别
                consultationType: item.consultationType || '1',       // 咨询类别
                consultationAttribute: item.consultationAttribute || '1', // 咨询属性
                answerFlag: item.answerFlag || '1',                   // 咨询是否回复
                platForm: item.platForm || '03',                      // 服务来源
                updateTime: formatDateTime(item.updateTime || Date.now()) // 数据更新时间（datetime）
            };
            // // ========== 选填字段（只有有值时才添加） ==========
            // addOptionalField(result, 'deptID', item.deptID);                           // 科室id
            // addOptionalField(result, 'deptName', item.deptName);                       // 科室名称
            // addOptionalField(result, 'doctorId', item.doctorCpde);                     // 医师ID
            // addOptionalField(result, 'doctorName', item.doctorName);                   // 医师姓名
            // addOptionalField(result, 'patientName', item.name);                        // 患者姓名
            // addOptionalField(result, 'age', item.age);                                 // 患者年龄
            // addOptionalField(result, 'mobile', item.mobile);                           // 患者联系电话
            // addOptionalField(result, 'applyDate', formatDateTime(item.applyDate));     // 咨询申请时间（datetime）
            // addOptionalField(result, 'startDate', formatDateTime(item.startDate));     // 咨询开始时间（datetime）
            // addOptionalField(result, 'endDate', formatDateTime(item.endDate));         // 咨询结束时间（datetime）
            // addOptionalField(result, 'paymentChannel', item.paymentChannel);           // 支付渠道
            // addOptionalField(result, 'consultationPrice', item.consultationPrice);     // 咨询价格
            // addOptionalField(result, 'content', item.content);                         // 用户咨询内容
            // addOptionalField(result, 'refuseTime', formatDateTime(item.refuseTime));   // 咨询拒绝/取消时间（datetime）
            // addOptionalField(result, 'refuseReason', item.reason);                     // 咨询拒绝/取消原因
            // addOptionalField(result, 'refuseType', item.refuseType);                   // 咨询拒绝类别
            // addOptionalField(result, 'processDataURL', item.processDataURL);           // 咨询过程数据查询地址
            // addOptionalField(result, 'fileds', item.fileds);                           // 文件列表
            // addOptionalField(result, 'birthday', formatDateTime(item.birthday));       // 患者出生日期（datetime）

            return result;
        });

        // 显示完整的发送数据
        console.log('📤 完整发送数据:');
        console.log(JSON.stringify(formattedData, null, 2));

        const result = await client.postWithRetry(config.services.consult, formattedData);
        console.log('✅ 在线咨询数据上报成功');
        console.log(`📋 响应结果: code:${result.code} | msg:${result.msg || '成功'} | data:${result.data ? '有数据' : '无数据'}`);
        return {
            success: true,
            count: formattedData.length,
            data: result,
            timestamp: formatDateTime(Date.now())
        };

    } catch (error) {
        console.error('在线咨询数据上报失败:', error.message);
        return {
            success: false,
            message: error.message,
            count: 0,
            data: null,
            timestamp: formatDateTime(Date.now())
        };
    }
}

// /**
//  * 在线复诊数据上报 - 已注释，暂不使用
//  * @param {object|array} referralData 复诊数据
//  * @returns {Promise<object>} 上报结果
//  */
// async function uploadReferralData(referralData) {
//     try {
//         const data = Array.isArray(referralData) ? referralData : [referralData];

//         // 数据格式化和验证
//         const formattedData = data.map(item => {
//             // 根据科室名称获取专业代码和名称
//             const subjectInfo = getSubjectByDeptName(item.deptName);

//             // 构建必填字段
//             const result = {
//                 // ========== 必填字段 ==========
//                 organID: config.organInfo.organID,                    // 全国统一的组织机构代码
//                 unitID: config.organInfo.unitID,                      // 监管平台机构ID
//                 organName: config.organInfo.organName,                // 机构名称
//                 bussID: item.orderId || '',                           // 互联网医院复诊记录Id
//                 subjectCode: item.subjectCode || subjectInfo.subjectCode, // 复诊医师所属专业代码(从字典获取)
//                 subjectName: item.subjectName || subjectInfo.subjectName, // 复诊医师所属专业名称(从字典获取)
//                 doctorCertID: item.doctorCertID || '330102199001011234',  // 医师身份证号（测试默认值）
//                 doctorName: item.doctorName || '',                    // 医生姓名
//                 patientCertID: item.patientCertID || '',              // 患者证件号
//                 patientName: item.patientName || '',                  // 患者姓名
//                 age: item.age || 0,                                   // 患者年龄
//                 sex: item.sex || '',                                  // 患者性别
//                 thisDiagnosis: formatDiagnosis(item.diagnosisList, 'current', item.answerFlag), // 本次复诊诊断（来自diagnostic-record）
//                 answerFlag: item.answerFlag || '1',                   // 复诊是否回复
//                 platForm: item.platForm || '03',                      // 服务来源平台编码
//                 updateTime: formatDateTime(item.updateTime || Date.now()) // 上传时间（datetime）
//             };

//             // // ========== 选填字段（只有有值时才添加） ==========
//             // addOptionalField(result, 'deptID', item.deptID);                           // 医师所属科室代码
//             // addOptionalField(result, 'deptName', item.deptName);                       // 医师所属科室名称
//             // addOptionalField(result, 'doctorId', item.doctorId);                       // 医生ID
//             // addOptionalField(result, 'patientCertType', item.patientCertType, '01');   // 患者证件类型
//             // addOptionalField(result, 'mobile', item.mobile);                           // 患者联系电话
//             // addOptionalField(result, 'costType', item.costType, '1');                  // 费别
//             // addOptionalField(result, 'guardianCertID', item.guardianCertID);           // 监护人身份证
//             // addOptionalField(result, 'guardianName', item.guardianName);               // 监护人姓名
//             // addOptionalField(result, 'guardianMobile', item.guardianMobile);           // 监护人手机
//             // addOptionalField(result, 'returnVisitType', item.returnVisitType, '1');    // 复诊类别
//             // addOptionalField(result, 'cardType', item.cardType);                       // 卡类型
//             // addOptionalField(result, 'cardNo', item.cardNo);                           // 卡号
//             // addOptionalField(result, 'diseasesHistory', item.diseasesHistory);         // 患者简要病史描述
//             // addOptionalField(result, 'originalDiagnosis', formatDiagnosis(item.diseases, 'original')); // 患者原诊断（来自consult-record）
//             // addOptionalField(result, 'applyDate', formatDateTime(item.applyDate));     // 在线复诊申请时间（datetime）
//             // addOptionalField(result, 'startDate', formatDateTime(item.startDate));     // 在线复诊开始时间（datetime）
//             // addOptionalField(result, 'endDate', formatDateTime(item.endDate));         // 在线复诊结束时间（datetime）
//             // addOptionalField(result, 'paymentChannel', item.paymentChannel);           // 支付渠道
//             // addOptionalField(result, 'returnVisitPrice', item.returnVisitPrice);       // 复诊金额
//             // addOptionalField(result, 'refuseTime', formatDateTime(item.refuseTime));   // 复诊拒绝/取消时间（datetime）
//             // addOptionalField(result, 'refuseReason', item.refuseReason);               // 复诊拒绝/取消原因
//             // addOptionalField(result, 'refuseType', item.refuseType);                   // 复诊拒绝类别
//             // addOptionalField(result, 'processDataURL', item.processDataURL);           // 复诊过程数据查询地址
//             // addOptionalField(result, 'birthDay', formatDateTime(item.birthDay));       // 患者出生日期（datetime）

//             return result;
//         });

//         const result = await client.postWithRetry(config.services.referral, formattedData);

//         return {
//             success: true,
//             count: formattedData.length,
//             data: result,
//             timestamp: formatDateTime(Date.now())
//         };

//     } catch (error) {
//         console.error('在线复诊数据上报失败:', error);
//         throw error;
//     }
// }

/**
 * 在线处方数据上报
 * @param {object|array} recipeData 处方数据
 * @returns {Promise<object>} 上报结果
 */
async function uploadRecipeData(recipeData) {
    try {
        const data = Array.isArray(recipeData) ? recipeData : [recipeData];

        // 数据格式化和验证
        const formattedData = data.map(item => {
            // 根据科室名称获取专业代码和名称
            const subjectInfo = getSubjectByDeptName(item.deptName);

            // 构建必填字段
            const result = {
                // ========== 必填字段 ==========
                organID: config.organInfo.organID,                    // 全国统一的组织机构代码
                unitID: config.organInfo.unitID,                      // 监管平台机构ID
                bussID: item.orderId || '',                           // 互联网医院复诊记录Id(处方对应的复诊id)
                originalDiagnosis: formatDiagnosis(item.diseases, 'original'), // 上次就诊诊断名称（来自consult-record）
                subjectCode: item.subjectCode || subjectInfo.subjectCode, // 开方医师所属专业代码(从字典获取)
                doctorCertID: item.doctorCertID || '330102199001011234',  // 医师身份证号（测试默认值）
                auditDoctorCertID: item.auditDoctorCertID || '330102199001011236', // 审方医师身份证号（测试默认值）
                patientCertID: item.idCard || '',              // 患者证件号
                sex: item.sex || '',                                  // 患者性别
                recipeUniqueID: item.medOrgOrderNo || '',             // 互联网医院处方唯一号
                recipeID: item.medOrgOrderNo || '',                   // 互联网医院处方号
                platForm: '03',                      // 护理服务来源
                datein: formatDateTime(item.createTime || Date.now()), // 处方日期（datetime）
                updateTime: formatDateTime(item.updateTime || Date.now()), // 上传时间（datetime）
                orderList: item.orderList || []                      // 处方列表数据集（药品列表）
            };

            // // ========== 选填字段（只有有值时才添加） ==========
            // addOptionalField(result, 'organName', config.organInfo.organName);        // 机构名称
            // addOptionalField(result, 'hosCode', item.hosCode);                         // 院区代码
            // addOptionalField(result, 'hosName', item.hosName);                         // 院区名称
            // addOptionalField(result, 'bussSource', item.bussSource, '4');              // 处方来源
            // addOptionalField(result, 'subjectName', item.subjectName || subjectInfo.subjectName); // 开方医师所属专业名称(从字典获取)
            // addOptionalField(result, 'deptID', item.deptID);                           // 医师所属科室代码
            // addOptionalField(result, 'deptName', item.deptName);                       // 医师所属科室名称
            // addOptionalField(result, 'doctorId', item.doctorId);                       // 医生ID
            // addOptionalField(result, 'doctorName', item.doctorName);                   // 医师姓名
            // addOptionalField(result, 'auditDoctorId', item.auditDoctorId);             // 审方医生ID
            // addOptionalField(result, 'auditDoctor', item.auditDoctor);                 // 审方医师姓名
            // addOptionalField(result, 'checkDate', formatDateTime(item.checkDate));     // 处方药师审核时间（datetime）
            // addOptionalField(result, 'patientCardType', item.patientCardType, '01');   // 患者证件类型
            // addOptionalField(result, 'patientName', item.patientName);                 // 患者姓名
            // addOptionalField(result, 'age', item.age);                                 // 患者年龄
            // addOptionalField(result, 'guardianCertID', item.guardianCertID);           // 监护人身份证（患者<6岁时必填）
            // addOptionalField(result, 'guardianName', item.guardianName);               // 监护人姓名（患者<6岁时必填）
            // addOptionalField(result, 'guardianMobile', item.guardianMobile);           // 监护人手机
            // addOptionalField(result, 'relation', item.relation);                       // 与本人关系
            // addOptionalField(result, 'mobile', item.mobile);                           // 患者联系电话
            // addOptionalField(result, 'costType', item.costType, '1');                  // 费别
            // addOptionalField(result, 'cardType', item.cardType);                       // 卡类型
            // addOptionalField(result, 'cardNo', item.cardNo);                           // 卡号
            // addOptionalField(result, 'allergyInfo', item.allergyInfo);                 // 过敏信息
            // addOptionalField(result, 'diseasesHistory', item.diseasesHistory);         // 患者简要病史描述
            // addOptionalField(result, 'recipeStatus', item.recipeStatus, '1');          // 处方状态
            // addOptionalField(result, 'recipeRecordNo', item.recipeRecordNo);           // 处方医保备案号
            // addOptionalField(result, 'deliveryType', item.deliveryType, '0');          // 处方配送方式
            // addOptionalField(result, 'deliveryFirm', item.deliveryFirm);               // 处方核销单位
            // addOptionalField(result, 'rationalFlag', item.rationalFlag, '0');          // 是否经过合理用药判断标志
            // addOptionalField(result, 'rationalDrug', item.rationalDrug);               // 合理用药审核结果
            // addOptionalField(result, 'CAInfo', item.CAInfo);                           // 处方CA认证的PDF文件id
            // addOptionalField(result, 'recipeFileId', item.recipeFileId);               // 处方笺文件id
            // addOptionalField(result, 'medicalFileId', item.medicalFileId);             // 患者病历文件id
            // addOptionalField(result, 'icdCode', item.icdCode);                         // 诊断ICD码
            // addOptionalField(result, 'icdName', item.icdName);                         // 初步诊断名称
            // addOptionalField(result, 'recipeType', item.recipeType, '1');              // 处方类型
            // addOptionalField(result, 'packetsNum', item.packetsNum);                   // 帖数（草药处方必填）
            // addOptionalField(result, 'effectivePeriod', item.effectivePeriod, 7);      // 处方效期
            // addOptionalField(result, 'startDate', formatDate(item.startDate));         // 处方开始日期（date）
            // addOptionalField(result, 'endDate', formatDate(item.endDate));             // 处方结束日期（date）
            // addOptionalField(result, 'totalFee', item.totalFee);                       // 处方金额
            // addOptionalField(result, 'isPay', item.isPay, '0');                        // 是否支付
            // addOptionalField(result, 'verificationStatus', item.verificationStatus, '0'); // 处方核销状态
            // addOptionalField(result, 'verificationTime', formatDateTime(item.verificationTime)); // 处方核销时间（datetime）
            // addOptionalField(result, 'verificationUnit', item.verificationUnit);       // 处方核销单位
            // addOptionalField(result, 'recipeFileIdURL', item.recipeFileIdURL);         // 处方笺文件浏览地址
            // addOptionalField(result, 'deliveryDate', formatDateTime(item.deliveryDate)); // 配送时间（datetime）
            // addOptionalField(result, 'birthDay', formatDateTime(item.birthDay));       // 患者出生日期（datetime）

            return result;
        });

        // 显示完整的发送数据
        console.log('📤 完整发送数据:');
        console.log(JSON.stringify(formattedData, null, 2));

        const result = await client.postWithRetry(config.services.recipe, formattedData);
        console.log('✅ 在线处方数据上报成功');
        console.log(`📋 响应结果: code:${result.code} | msg:${result.msg || '成功'} | data:${result.data ? '有数据' : '无数据'}`);

        return {
            success: true,
            count: formattedData.length,
            data: result,
            timestamp: formatDateTime(Date.now())
        };

    } catch (error) {
        console.error('在线处方数据上报失败:', error.message);
        return {
            success: false,
            message: error.message,
            count: 0,
            data: null,
            timestamp: formatDateTime(Date.now())
        };
    }
}

module.exports = {
    uploadConsultData,
    // uploadReferralData, // 已注释，暂不使用
    uploadRecipeData
};
