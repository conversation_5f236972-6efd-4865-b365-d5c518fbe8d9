const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "updateCustomerInHospitalTime":
      return await updateCustomerInHospitalTime(content);
    case "pushCustomerTeamId":
      return await pushCustomerTeamId(content);
    case "updateCustomerPersonResponsibles":
      return await updateCustomerPersonResponsibles(content);
  }
};

// 更新到院时间
async function updateCustomerInHospitalTime(content) {
  const { corpId, customerId, inHospitalTime } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  const query = { corpId, _id: customerId };
  const customer = await db.collection("member").findOne(query);

  if (!customer) return { success: false, message: "客户不存在" };

  let { inHospitalTimes } = customer;
  if (!inHospitalTimes || !Array.isArray(inHospitalTimes)) inHospitalTimes = [];

  // 判断 inHospitalTimes 存在的时间戳 是否跟 inHospitalTime 是在同一天
  const inHospitalTimeDay = dayjs(inHospitalTime).format("YYYY-MM-DD");
  const inHospitalTimesDay = inHospitalTimes.map((time) =>
    dayjs(time).format("YYYY-MM-DD")
  );

  if (inHospitalTimesDay.includes(inHospitalTimeDay))
    return { success: false, message: "该天已存在到院时间" };

  inHospitalTimes.push(inHospitalTime);
  // 根据大小排序
  inHospitalTimes.sort((a, b) => a - b);

  const res = await db.collection("member").updateOne(
    { _id: customer._id },
    { $set: { inHospitalTimes } }
  );

  return { success: true, data: res, message: "更新到院时间成功" };
}

// 更新客户负责人
async function updateCustomerPersonResponsibles(item) {
  try {
    const { id, personResponsibles, teamId } = item;
    const res = await db.collection("member").updateOne(
      { _id: id },
      { $set: { personResponsibles, teamId } }
    );

    return {
      message: "添加成功",
      success: true,
      data: res,
    };
  } catch {
    return {
      message: "添加失败",
      success: false,
    };
  }
}

// 添加客户团队
async function pushCustomerTeamId(content) {
  const { customerId, teamId } = content;
  const res = await db.collection("member").updateOne(
    { _id: customerId },
    { $addToSet: { teamId } }
  );

  return {
    message: "添加成功",
    success: true,
    data: res,
  };
}
