const dayjs = require('dayjs')
exports.formatUploadRxData = function (data) {
  return {
    "patientid": data.patientId,
    "orderno": data.medOrgOrderNo,
    "doctorcode": data.doctorCode,
    "unitcode": data.unitCode,
    "hospid": "108",
    "phar_code": data.pharmacist<PERSON><PERSON>,
    "phar_name": data.pharmacist,
    "phar_chk_time": dayjs(data.auditTime).format('YYYY-MM-DD HH:mm:ss'),
    "details": data.drugs.map(i => ({
      "drug_id": i.insurance_code,
      "drug_name": i.drugName,
      "drug_dosform": "",
      "drug_spec": i.specification || "",
      "drug_pric": "0",
      "drug_cnt": i.quantity,
      "drug_sumamt": "0",
      "medc_way_codg": i.usageCode,
      "medc_way_dscr": i.usageName,
      "medc_days": i.days,
      "remark": "",
      "limit_flag": "0",
      "used_frqu_codg": i.frequencyCode,
      "used_frqu_name": i.frequencyName,
      "sin_doscnt": i.dosage,
      "sin_dosunt": i.dosage_unit,
      "unit": i.unit
    })),
    "icds": data.diagnosisList.map(i => ({
      "icd_code": i.code,
      "icd_name": i.name
    }))
  }
}