const common = require("../../common");
const zytHis = require("../zyt-his");
const dayjs = require("dayjs");
const { getConfig } = require("../utils/config");
const tencentIM = require("../tencent-im");
const trigger = require("./trigger");
const { ObjectId } = require("mongodb");
const timeApi = require("../hlw-time-duration");
const orderStatus = require("../stats/order-status");

const hisPayStatus = {
  0: "已支付",
  1: "已退费",
  5: "未支付",
  x: "已作废",
};
const refundStatus = {
  INIT: "未处理",
  PASS: "已通过",
  REJECT: "已拒绝",
};
const RefundOrderScene = {
  DOCTORAGREEREFUND: "患者已退费",
  OFFLINEREFUND: "患者线下已退费",
}; // 咨询订单退费场景

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addConsultOrder":
      return await addConsultOrder(item);
    case "addDrugStoreConsultOrder":
      return await addDrugStoreConsultOrder(item);
    case "getPatientOrder":
      return await getPatientOrder(item);
    case "getPatientOrderList":
      return await getPatientOrderList(item);
    case "getDrugStoreOrderList":
      return await getDrugStoreOrderList(item);
    case "deleteConsultOrder":
      return await deleteConsultOrder(item);
    case "updateConsultOrder":
      return await updateConsultOrder(item);
    case "getConsultOrder":
      return await getConsultOrder(item);
    case "updateConsultOrderStatus":
      return await updateConsultOrderStatus(item);
    case "refundConsultOrder":
      return await refundConsultOrder(item);
    case "acceptConsultOrder":
      return await acceptConsultOrder(item);
    case "completeConsultOrder":
      return await completeConsultOrder(item);
    case "finishConsultOrder":
      return await finishConsultOrder(item);
    case "startConsultOrder":
      return await startConsultOrder(item);
    case "refreshOrderPayStatus":
      return await refreshOrderPayStatus(item);
    case "cancelConsultOrder":
      return await cancelConsultOrder(item);
    case "doctorHasPendingOrder":
      return await doctorHasPendingOrder(item);
    case "refundFeeAndOrder":
      return await refundFeeAndOrder(item);
    case "serviceFinishConsultOrder":
      return await serviceFinishConsultOrder(item);
    case "getHisStoreRegList":
      return await getHisStoreRegList(item);
    case "validHlwOrderConfig":
      return await validHlwOrderConfig(item);
    case "customerRefundOrder":
      return await customerRefundOrder(item);
    case "getOrderPatientList":
      return await getOrderPatientList(item);
    case "getHlwOrderList":
      return await getHlwOrderList(item);
    case "applyRefundOrder":
      return await applyRefundOrder(item);
    case "getRefundOrderList":
      return await getRefundOrderList(item);
    case "handleRefundOrder":
      return await handleRefundOrder(item);
    default:
      return {
        success: false,
        message: "请求失败!",
      };
  }
};
// 咨询订单库 数据库是 consult-order
async function addConsultOrder(item) {
  let { params, corpId } = item;
  if (typeof params.doctorCode !== "string") {
    return { success: false, message: "医生编号不能为空" };
  }
  const orderId = `H${common.generateRandomString(10)}`;
  const registerId = `H${common.generateRandomString(15)}`;
  params.orderId = orderId;
  params.registerId = registerId;
  params.createTime = Date.now();
  params.orderStatus = "pending";
  params.payStatus = "pending";
  params.corpId = corpId;
  const { payDuration, acceptDuration = 30 } = await getConfig(corpId);
  params.payExpireTime = dayjs().add(payDuration, "minute").valueOf();
  // params.expireTime = dayjs().add(acceptDuration, "minute").valueOf();
  try {
    const storeApi = require("../drug-store");
    const { success, list, message } = await storeApi(
      {
        type: "getDrugStoreList",
        storeId:
          typeof params.drugStoreId === "string" ? params.drugStoreId : "",
        pageSize: 1,
      },
      db
    );
    if (!success) {
      return { success, message };
    }
    const store = Array.isArray(list) ? list[0] : null;
    const chargeItem =
      store && Array.isArray(store.chargeItems) ? store.chargeItems[0] : null;
    if (!store) {
      return { success: false, message: "为查询到药房信息" };
    }
    if (!chargeItem || !chargeItem.code) {
      return { success: false, message: "当前药房未配置互联网诊查费" };
    }
    params.chargeCode = chargeItem.code;
    params.chargeFee = chargeItem.fee;
    params.drugStoreName = store.name;
    params.drugStoreNo = store.login_no;
    params.orderSource = "ALIPAY_MINI"; // 支付宝小程序
    const doctorCode = params.doctorCode.trim();
    // 指定医生
    if (doctorCode) {
      const doctor = await db.collection("hlw-doctor").findOne(
        { doctorNo: doctorCode, job: "doctor" },
        {
          projection: {
            doctorNo: 1,
            doctorName: 1,
            title: 1,
            deptName: 1,
            deptCode: 1,
            onlineStatus: 1,
          },
        }
      );
      if (!doctor) {
        return { success: false, message: "未查询到医生" };
      }
      if (doctor.onlineStatus !== "online") {
        return { success: false, message: "医生未在线, 请重新选择医生" };
      }
      params.doctorName = doctor.doctorName || "";
      params.doctorTitle = doctor.title || "";
      params.deptName = doctor.deptName || "";
      params.unitCode = doctor.deptCode || "";
      params.doctorCode = doctor.doctorNo;
    } else {
      // 随机选取一位医生 (单量少的权重大)
      const doctorApi = require("../hlw-doctor");
      const data = await doctorApi({ type: "getOptimalRecommendDoctor" }, db);
      if (data.success && data.data) {
        params.doctorName = data.data.doctorName || "";
        params.doctorCode = data.data.doctorNo;
        params.doctorTitle = data.data.title || "";
        params.deptName = data.data.deptName || "";
        params.unitCode = data.data.deptCode || "";
      } else {
        return { success: false, message: data.message };
      }
    }
    // const has = await hasPendingOrder(params.patientId);
    // if (has) {
    //   return {
    //     success: false,
    //     message: "很抱歉！您有处理中的问诊订单，暂不支持发起新的问诊",
    //   };
    // }

    const { insertedId } = await db
      .collection("consult-order")
      .insertOne(params);
    if (insertedId) {
      const { patientId, doctorCode, unitCode, registerId } = params;
      const { success, data, message } = await zytHis({
        type: "registration",
        patientId,
        doctorCode,
        unitCode,
        registerId,
        chargecode: params.chargeCode,
        reg_fee: params.chargeFee,
        shopid: params.drugStoreNo,
        shopname: params.drugStoreName,
      });
      if (success && data && data.register && data.register.medorg_order_no) {
        db.collection("consult-order").updateOne(
          { _id: insertedId },
          { $set: { medorg_order_no: data.register.medorg_order_no } }
        );
        return {
          success: true,
          message: "新增成功",
          data: {
            orderId,
            registerId,
            medorg_order_no: data.register.medorg_order_no,
            doctorCode: params.doctorCode,
            doctorName: params.doctorName,
            doctorTitle: params.doctorTitle,
            deptName: params.deptName,
            deptCode: params.unitCode,
          },
        };
      }
      // 如果his接口调用失败 删除订单
      await db.collection("consult-order").deleteOne({ _id: insertedId });
      return { success: false, message: message || "新增失败" };
    }
    return {
      success: false,
      message: "新增失败",
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "新增失败",
    };
  }
}

async function addDrugStoreConsultOrder(ctx) {
  const { params } = ctx;
  const { registerId } = params || {};
  if (!registerId) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const total = await db.collection("consult-order").countDocuments({
      registerId,
    });
    if (total > 0) {
      return {
        success: false,
        message: "当前挂号已存在订单",
      };
    }
    const { status, settlement } = await zytHis({
      type: "getPayStatus",
      patientId: params.patientId,
      registerId,
      medorgOrderNo: registerId,
    });
    if (status !== "0") {
      return {
        success: false,
        message: hisPayStatus[status]
          ? `当前挂号${hisPayStatus[status]}`
          : "查询当前挂号支付状态失败",
      };
    }
    const orderId = `H${common.generateRandomString(10)}`;
    params.settlement = settlement;
    params.createTime = Date.now();
    params.orderId = orderId;
    params.orderStatus = "pending";
    params.payStatus = "success";
    params.medorg_order_no = registerId;
    params.orderSource = "MATEPAD"; //  门店pad
    if (ctx.corpId) {
      params.corpId = ctx.corpId;
    }
    const doctor = await db.collection("hlw-doctor").findOne(
      { doctorNo: typeof params.doctorCode === 'string' ? params.doctorCode : '', job: "doctor" },
      {
        projection: {
          _id: 0,
          onlineStatus: 1,
        },
      }
    );
    if (!doctor) {
      return { success: false, message: "未查询到挂号医生" };
    }
    if (doctor.onlineStatus !== "online") {
      return { success: false, message: "医生已下线。请联系门店工作人员退号，如需继续就诊请重新挂号。" };
    }
    // const { acceptDuration = 30 } = await getConfig(corpId);
    // params.expireTime = dayjs().add(acceptDuration, "minute").valueOf();
    const { insertedId } = await db
      .collection("consult-order")
      .insertOne(params);
    return {
      success: true,
      message: "新增成功",
      data: {
        orderId,
        registerId: params.registerId,
        insertedId,
        medorg_order_no: params.registerId,
      },
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "新增失败",
    };
  }
}

async function cancelConsultOrder(item) {
  const { orderId, corpId } = item;
  try {
    // 获取订单
    const res = await db.collection("consult-order").updateOne(
      { orderId, corpId },
      {
        $set: {
          orderStatus: "cancelled",
          payStatus: "cancelled",
          payResult: "用户取消订单",
          updateTime: Date.now(),
        },
      }
    );
    return {
      success: true,
      message: "取消成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "取消失败",
    };
  }
}

async function getPatientOrder(item) {
  const { orderId } = item;
  if (!orderId) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const [data] = await db
      .collection("consult-order")
      .aggregate([
        { $match: { orderId: orderId } },
        {
          $lookup: {
            from: "diagnostic-record", // 连接到 diagnostic-record 集合
            localField: "orderId", // consult-order 集合中的 orderId 字段
            foreignField: "orderId", // diagnostic-record 集合中的 orderId 字段
            as: "diagnostic", // 返回的联接结果存储在 diagnostic 字段中
          },
        },
        {
          $addFields: {
            diagnostic: {
              $filter: {
                input: "$diagnostic",
                as: "diagnosticRecord",
                cond: { $eq: ["$$diagnosticRecord.status", "PASS"] },
              },
            },
          },
        },
        {
          $addFields: {
            hasPassDiagnostic: { $gt: [{ $size: "$diagnostic" }, 0] },
          },
        },
        {
          $project: {
            diagnostic: 0, // 排除 diagnostic 字段
          },
        },
        {
          $limit: 1,
        },
      ])
      .toArray();
    if (
      data &&
      data.payExpireTime < Date.now() &&
      data.payStatus === "pending"
    ) {
      data.payStatus = "expired";
      data.payResult = "超时未支付";
    }
    if (
      data &&
      ["processing", "pending", "completed"].includes(data.orderStatus) &&
      data.expireTime < Date.now()
    ) {
      data.orderStatus = "cancelled";
    }
    return {
      success: true,
      data,
      message: "查询成功",
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

async function getDrugStoreOrderList(ctx) {
  const { storeId, name } = ctx;
  if (typeof storeId !== "string" || storeId.trim() === "") {
    return { success: false, message: "店铺id不能为空" };
  }
  const page = parseInt(ctx.page) > 0 ? parseInt(ctx.page) : 1;
  const pageSize = parseInt(ctx.pageSize) > 0 ? parseInt(ctx.pageSize) : 20;
  const date =
    ctx.date && dayjs(ctx.date).isValid() ? dayjs(ctx.date) : dayjs();
  const createTime = {
    $gte: date.startOf("day").valueOf(),
    $lte: date.endOf("day").valueOf(),
  };
  try {
    const query = {
      drugStoreNo: storeId.trim(),
      createTime,
      orderSource: "MATEPAD",
    };
    if (typeof name == "string" && name.trim() !== "") {
      query.$or = [
        { name: { $regex: name.trim(), $options: "i" } },
        { doctorName: { $regex: name.trim(), $options: "i" } },
      ];
    }
    const list = await db
      .collection("consult-order")
      .aggregate([
        { $match: query },
        { $sort: { createTime: -1 } },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $lookup: {
            from: "diagnostic-record", // 连接到 diagnostic-record 集合
            localField: "orderId", // consult-order 集合中的 orderId 字段
            foreignField: "orderId", // diagnostic-record 集合中的 orderId 字段
            as: "diagnostic", // 返回的联接结果存储在 diagnostic 字段中
          },
        },
        {
          $addFields: {
            diagnostic: {
              $filter: {
                input: "$diagnostic",
                as: "diagnosticRecord",
                cond: { $eq: ["$$diagnosticRecord.status", "PASS"] },
              },
            },
          },
        },
        {
          $addFields: {
            hasPassDiagnostic: { $gt: [{ $size: "$diagnostic" }, 0] },
          },
        },
      ])
      .toArray();
    const total = await db.collection("consult-order").countDocuments(query);
    const todayCount = await db.collection("consult-order").countDocuments({
      drugStoreNo: storeId.trim(),
      createTime: {
        $gte: dayjs().startOf("day").valueOf(),
        $lte: dayjs().endOf("day").valueOf(),
      },
      orderSource: "MATEPAD",
    });
    return {
      success: true,
      list,
      total,
      page,
      pageSize,
      todayCount,
      pages: Math.ceil(total / pageSize),
      message: "查询成功",
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询失败",
    };
  }
}

async function acceptConsultOrder(item) {
  const { orderId, corpId, doctorCode } = item;
  try {
    const item = await db.collection("consult-order").findOne(
      { orderId },
      {
        projection: {
          expireTime: 1,
          createTime: 1,
          doctorCode: 1,
          orderStatus: 1,
          doctorName: 1,
        },
      }
    );
    if (!item) {
      return { success: false, message: "订单不存在" };
    }
    if (
      dayjs().startOf("day").valueOf() > item.expireTime ||
      dayjs().startOf("day").valueOf() > item.createTime
    ) {
      return { success: false, message: "订单已过期，请刷新" };
    }
    if (item.orderStatus !== "pending") {
      return { success: false, message: "订单已失效，请刷新" };
    }
    const { consultationDuration } = await getConfig(corpId);
    const expireTime = dayjs().add(consultationDuration, "minute").valueOf();
    await updateConsultOrderStatus({
      orderId,
      orderStatus: "processing",
      expireTime,
      doctorCode: item.doctorCode,
      corpId,
    });
    timeApi(
      {
        type: "addDoctorOrderDuration",
        doctorCode: item.doctorCode,
        doctorName: item.doctorName,
        orderId,
        createTime: item.createTime,
      },
      db
    );

    // 接受问诊 发送系统消息
    await tencentIM({
      type: "sendSystemNotification",
      formAccount: orderId,
      toAccount: doctorCode,
      SyncOtherMachine: 1,
      corpId,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "ACCEPTCONSULT",
            Ext: "您已接诊了患者发起的图文问诊服务，请及时回复处理。感谢您的辛勤付出！",
          },
        },
      ],
    });

    // 删除定时任务
    await trigger({
      type: "deleteDelayedTask",
      triggerTaskId: orderId,
      corpId,
    });
    // 从新建一个定时任务
    await trigger({
      type: "createDelayedTask",
      triggerTaskId: orderId,
      endTime: expireTime,
      corpId,
    });
    return {
      success: true,
      message: "接诊成功",
      expireTime,
    };
  } catch (err) {
    return {
      success: false,
      message: "接诊失败",
    };
  }
}
// 结束问诊
async function finishConsultOrder(item) {
  // 判断是否有正在审核的单子
  const count = await db.collection("diagnostic-record").countDocuments({
    orderId: item.orderId,
    status: "INIT",
  });
  if (count > 0) {
    return {
      success: false,
      message: "存在正在审核的诊断单，不能结束咨询",
    };
  }
  return await serviceFinishConsultOrder(item);
}
async function serviceFinishConsultOrder(item) {
  let { orderId, doctorCode, corpId } = item;
  try {
    // 获取订单
    if (!doctorCode || !corpId) {
      const order = await db.collection("consult-order").findOne({ orderId });
      doctorCode = order.doctorCode;
      corpId = order.corpId;
      if (
        order.orderStatus === "finished" ||
        order.orderStatus === "cancelled"
      ) {
        return {
          success: false,
          message: "订单已结束",
        };
      }
    }
    // 更新订单状态
    await closeConsultOrder({ orderId, corpId, doctorCode });
    await setDiagnosticExpired({ orderId });
    return {
      success: true,
      message: "完成成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "完成失败",
    };
  }
}

async function closeConsultOrder({ orderId, doctorCode, corpId }) {
  // 删除定时任务
  await trigger({
    type: "deleteDelayedTask",
    triggerTaskId: orderId,
  });
  await tencentIM({
    type: "sendSystemNotification",
    formAccount: doctorCode,
    toAccount: orderId,
    SyncOtherMachine: 1,
    corpId,
    msgBody: [
      {
        MsgType: "TIMCustomElem",
        MsgContent: {
          Data: "FINISHED",
          Ext: "本次问诊已结束，不能继续发消息。",
        },
      },
    ],
  });
  await updateConsultOrderStatus({
    orderId,
    orderStatus: "finished",
    doctorCode,
    corpId,
  });
}

// 完成订单
async function completeConsultOrder(item) {
  let { orderId, doctorCode, sendText, corpId } = item;
  try {
    // 获取订单
    if (doctorCode) {
      const order = await db.collection("consult-order").findOne({ orderId });
      doctorCode = order.doctorCode;
    }
    // 更新订单状态
    await updateConsultOrderStatus({
      orderId,
      orderStatus: "completed",
    });
    // 删除定时任务
    await trigger({
      type: "deleteDelayedTask",
      triggerTaskId: orderId,
    });
    // 发送系统消息
    tencentIM({
      type: "sendSystemNotification",
      formAccount: doctorCode,
      toAccount: orderId,
      corpId,
      SyncOtherMachine: 1,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "COMPLETED",
            Desc: sendText,
          },
        },
      ],
    });
    return {
      success: true,
      message: "完成成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "完成失败",
    };
  }
}
// 删除咨询订单
async function deleteConsultOrder(item) {
  const { orderId } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("consult-order").deleteOne({
      orderId,
    });
    return {
      success: true,
      message: "删除成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}
// 更新咨询订单
async function updateConsultOrder(item) {
  const { orderId, params } = item;
  try {
    // 调用mongo 数据库更新
    const res = await db.collection("consult-order").updateOne(
      { orderId },
      {
        $set: {
          ...params,
          updateTime: Date.now(),
        },
      }
    );
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
// 获取咨询订单 分页查询 page 页码 pageSize 每页数量 数据库分页
async function getConsultOrder(item) {
  const {
    page,
    pageSize,
    orderId,
    hospitalId,
    patientId,
    idCard,
    orderStatus,
    mergeStatus,
    doctorCode,
    corpId,
    showPassdiagnostic = false,
    hasPassDiagnostic,
    drugStoreId,
    orderSource,
  } = item;
  const query = {};
  if (orderId) query.orderId = orderId;
  if (hospitalId) query.hospitalId = hospitalId;
  if (patientId) query.patientId = patientId;
  if (idCard) query.idCard = idCard;
  // if (corpId) query.corpId = corpId;
  if (doctorCode) query.doctorCode = doctorCode;
  if (orderStatus) query.orderStatus = orderStatus;
  if (drugStoreId) query.drugStoreId = drugStoreId;
  if (orderSource) query.orderSource = orderSource;
  if (mergeStatus === "需处理") {
    query.orderStatus = {
      $in: ["pending", "processing", "completed"],
    };
  }
  // 已完成 包括已完成和已取消 两种状态 或者 过期时间小于当前时间 注意 是或的关系 使用 or
  if (mergeStatus === "已处理") {
    query.orderStatus = {
      $in: ["cancelled", "finished"],
    };
  }

  try {
    let res, total;

    // 优化：分离查询，避免使用 $lookup 聚合操作
    if (showPassdiagnostic) {
      // 方案1：如果需要根据 hasPassDiagnostic 过滤，先查询诊断记录获取相关的 orderId
      if (typeof hasPassDiagnostic === "boolean") {
        // 先查询所有通过审核的诊断记录的 orderId
        const passedDiagnosticOrderIds = await db
          .collection("diagnostic-record")
          .distinct("orderId", { status: "PASS" });

        // 根据 hasPassDiagnostic 条件调整查询
        if (hasPassDiagnostic) {
          // 只查询有通过诊断的订单
          if (query.orderId) {
            // 如果已经指定了orderId，检查它是否在通过的列表中
            if (!passedDiagnosticOrderIds.includes(query.orderId)) {
              query.orderId = null; // 不在列表中，设为null表示无结果
            }
          } else {
            // 没有指定orderId，查询所有通过的订单
            if (passedDiagnosticOrderIds.length > 0) {
              query.orderId = { $in: passedDiagnosticOrderIds };
            } else {
              // 如果没有通过的诊断记录，返回空结果
              query.orderId = null;
            }
          }
        } else {
          // 只查询没有通过诊断的订单
          if (query.orderId) {
            // 如果已经指定了orderId，检查它是否不在通过的列表中
            if (passedDiagnosticOrderIds.includes(query.orderId)) {
              query.orderId = null; // 在列表中，设为null表示无结果
            }
          } else {
            // 没有指定orderId，排除所有通过的订单
            if (passedDiagnosticOrderIds.length > 0) {
              query.orderId = { $nin: passedDiagnosticOrderIds };
            }
            // 如果没有通过的诊断记录，则查询所有订单（不需要额外条件）
          }
        }

        // 如果查询条件导致没有结果，直接返回
        if (
          query.orderId === null ||
          (Array.isArray(query.orderId?.$in) && query.orderId.$in.length === 0)
        ) {
          return {
            success: true,
            message: "查询成功",
            data: [],
            total: 0,
            pages: 0,
          };
        }
      }

      // 先查询订单列表
      res = await db
        .collection("consult-order")
        .find(query)
        .sort({ createTime: -1 })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray();

      // 获取总数
      total = await db.collection("consult-order").countDocuments(query);

      // 如果有订单结果，批量查询诊断记录
      if (res.length > 0) {
        const orderIds = res.map((order) => order.orderId);

        // 批量查询这些订单的通过审核的诊断记录
        const diagnosticRecords = await db
          .collection("diagnostic-record")
          .find(
            {
              orderId: { $in: orderIds },
              status: "PASS",
            },
            { projection: { orderId: 1, _id: 0 } }
          )
          .toArray();

        // 创建 orderId 到 hasPassDiagnostic 的映射
        const diagnosticMap = new Set(
          diagnosticRecords.map((record) => record.orderId)
        );

        // 为每个订单添加 hasPassDiagnostic 字段
        res = res.map((order) => ({
          ...order,
          hasPassDiagnostic: diagnosticMap.has(order.orderId),
        }));
      }
    } else {
      // 如果不需要诊断信息，直接查询订单
      if (typeof hasPassDiagnostic === "boolean") {
        // 仍然需要根据诊断状态过滤
        const passedDiagnosticOrderIds = await db
          .collection("diagnostic-record")
          .distinct("orderId", { status: "PASS" });

        if (hasPassDiagnostic) {
          // 只查询有通过诊断的订单
          if (query.orderId) {
            // 如果已经指定了orderId，检查它是否在通过的列表中
            if (!passedDiagnosticOrderIds.includes(query.orderId)) {
              query.orderId = null; // 不在列表中，设为null表示无结果
            }
          } else {
            // 没有指定orderId，查询所有通过的订单
            if (passedDiagnosticOrderIds.length > 0) {
              query.orderId = { $in: passedDiagnosticOrderIds };
            } else {
              // 如果没有通过的诊断记录，返回空结果
              query.orderId = null;
            }
          }
        } else {
          // 只查询没有通过诊断的订单
          if (query.orderId) {
            // 如果已经指定了orderId，检查它是否不在通过的列表中
            if (passedDiagnosticOrderIds.includes(query.orderId)) {
              query.orderId = null; // 在列表中，设为null表示无结果
            }
          } else {
            // 没有指定orderId，排除所有通过的订单
            if (passedDiagnosticOrderIds.length > 0) {
              query.orderId = { $nin: passedDiagnosticOrderIds };
            }
            // 如果没有通过的诊断记录，则查询所有订单（不需要额外条件）
          }
        }

        if (
          query.orderId === null ||
          (Array.isArray(query.orderId?.$in) && query.orderId.$in.length === 0)
        ) {
          return {
            success: true,
            message: "查询成功",
            data: [],
            total: 0,
            pages: 0,
          };
        }
      }

      res = await db
        .collection("consult-order")
        .find(query)
        .sort({ createTime: -1 })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray();

      total = await db.collection("consult-order").countDocuments(query);
    }

    // 处理订单状态
    const array = res.map((item) => {
      if (item.payExpireTime < Date.now() && item.payStatus === "pending") {
        item.payStatus = "expired";
        item.payResult = "超时未支付";
      }
      if (
        ["processing", "pending", "completed"].includes(item.orderStatus) &&
        item.expireTime < Date.now()
      ) {
        item.orderStatus = "cancelled";
      }
      return item;
    });

    return {
      success: true,
      message: "查询成功",
      data: array,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

// 修改问诊状态
async function updateConsultOrderStatus(item) {
  const {
    orderId,
    orderStatus,
    payStatus,
    expireTime,
    doctorCode,
    corpId,
    reason,
  } = item;
  try {
    let query = {};
    if (orderStatus) query.orderStatus = orderStatus;
    if (payStatus) query.payStatus = payStatus;
    if (expireTime) query.expireTime = expireTime;
    if (typeof reason === "string") query.reason = reason;
    // 当医生开始处理, 开始加过期时间, 过期时间为30分钟
    const res = await db.collection("consult-order").updateOne(
      { orderId },
      {
        $set: {
          ...query,
          updateTime: Date.now(),
        },
      }
    );
    // 删除会话
    if (orderStatus === "cancelled" || orderStatus === "finished") {
      const res = await tencentIM({
        type: "deleteSession",
        fromAccount: doctorCode,
        toAccount: orderId,
        corpId: corpId,
      });
    }
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
// 退费并退款接口
async function refundConsultOrder(item) {
  // 判断是否有正在审核的单子
  const count = await db.collection("diagnostic-record").countDocuments({
    orderId: item.orderId,
    status: "INIT",
  });
  if (count > 0) {
    return {
      success: false,
      message: "存在正在审核的诊断单，不能退单",
    };
  }
  // 退费并退诊
  return await refundFeeAndOrder(item);
}
// 退费并退诊 换一个方法名
async function refundFeeAndOrder(item) {
  let { orderId, reason, order } = item;
  try {
    // 1. 更新订单状态
    if (!order) {
      order = await db.collection("consult-order").findOne({ orderId });
      if (!order) {
        return {
          success: false,
          message: "订单不存在",
        };
      }
    }
    if (["cancelled", "finished"].includes(order.orderStatus)) {
      return {
        success: false,
        message: "订单已取消或已完成，不能退单",
      };
    }
    const { doctorCode, corpId, orderSource } = order;
    // 如果订单已完成，且 审核通过
    if (order.orderStatus === "completed") {
      const count = await db.collection("diagnostic-record").countDocuments({
        orderId,
        status: "PASS",
      });
      if (count > 0) {
        return await serviceFinishConsultOrder({ orderId, doctorCode, corpId });
      }
    }
    // 调用退费接口
    // 2. 退费
    let payStatus = "",
      payResult = "";
    if (orderSource !== "MATEPAD") {
      const { success, message } = await zytHis({
        type: "hlwRefund",
        patientId: order.patientId,
        registerId: order.registerId,
        medorgOrderNo: order.medorgOrderNo,
      });
      payStatus = success ? "refunded" : "refundFailed";
      payResult = success ? "退费成功" : message;
    }

    // 更新诊断单状态
    await setDiagnosticExpired({ orderId });
    await tencentIM({
      type: "sendSystemNotification",
      formAccount: doctorCode,
      toAccount: orderId,
      SyncOtherMachine: 1,
      corpId,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "CANCELLED",
            Desc: "notification",
            Ext: reason,
          },
        },
      ],
    });

    // 3. 更新订单状态
    await updateConsultOrderStatus({
      orderId,
      orderStatus: "cancelled",
      payStatus,
      payResult,
      doctorCode,
      corpId,
      reason: typeof reason == "string" ? reason : "",
    });
    return {
      success: true,
      message: "退费成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "退费失败",
    };
  }
}

// // 问诊开始
async function startConsultOrder(item) {
  const { orderId, corpId, doctorCode } = item;
  const { consultationDuration, acceptDuration } = await getConfig(corpId);
  const expireTime = dayjs().add(acceptDuration, "minute").valueOf();
  try {
    const item = await db
      .collection("consult-order")
      .findOne({ orderId }, { projection: { expireTime: 1, orderStatus: 1 } });
    if (!item) {
      return { success: false, message: "订单不存在" };
    }
    if (item.orderStatus !== "pending") {
      return { success: false, message: "当前订单已失效，请刷新" };
    }
    if (item.expireTime) {
      return { success: true, message: "订单已开始" };
    }
    // 更新订单过期时间
    await db
      .collection("consult-order")
      .updateOne({ orderId }, { $set: { expireTime } });
    // 从新建一个定时任务
    await trigger({
      type: "createDelayedTask",
      triggerTaskId: orderId,
      endTime: expireTime,
      corpId,
    });
    await tencentIM({
      type: "sendSystemNotification",
      formAccount: orderId,
      toAccount: doctorCode,
      SyncOtherMachine: 2,
      corpId,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "STARTCONSULT",
            Ext: `问诊已开始，本次问诊可持续${consultationDuration}分钟`,
          },
        },
      ],
    });
    // 患者向医生发送一个系统消息，告诉医生问诊已经开始
    setTimeout(() => {
      tencentIM({
        type: "sendSystemNotification",
        formAccount: doctorCode,
        toAccount: orderId,
        SyncOtherMachine: 2,
        corpId,
        msgBody: [
          {
            MsgType: "TIMCustomElem",
            MsgContent: {
              Data: "START",
              Ext: "已向医生发起问诊，请等待医生回复！请不要退出当前聊天窗口",
            },
          },
        ],
      });
    }, 1000);
    return {
      success: true,
      message: "开始成功",
      expireTime,
    };
  } catch (err) {
    return {
      success: false,
      message: "开始失败",
    };
  }
}

async function refreshOrderPayStatus(params) {
  const { orderId, patientId, registerId, medorgOrderNo } = params;
  if (!orderId || !patientId || !registerId || !medorgOrderNo) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const res = await zytHis({
      type: "getPayStatus",
      patientId,
      registerId,
      medorgOrderNo,
    });

    if (res && res.settlement) {
      await db.collection("consult-order").updateOne(
        {
          orderId,
          patientId,
          registerId,
          medorg_order_no: medorgOrderNo,
        },
        {
          $set: {
            settlement: res.settlement,
          },
        }
      );
    }
    return { ...res };
  } catch (e) {
    return { success: false, message: e.message || "刷新失败" };
  }
}

/**
 * 是否有进行中的订单 有则不能创建新的订单
 * @param {patientId}; 患者id
 * @returns {boolean}
 */
async function hasPendingOrder(patientId) {
  try {
    const count = await db.collection("consult-order").countDocuments({
      patientId,
      orderStatus: { $in: ["pending", "processing", "completed"] },
      expireTime: { $gt: Date.now() },
    });
    return count > 0;
  } catch (e) {
    return false;
  }
}

/**
 * 查询医生是否存在进行中的订单
 * @param {*} params
 * @returns
 */
async function doctorHasPendingOrder(params) {
  try {
    const count = await db.collection("consult-order").countDocuments({
      doctorCode: params.doctorCode,
      orderStatus: { $in: ["pending", "processing", "completed"] },
    });
    return { success: true, data: count > 0, message: "查询成功" };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}
// 设置处方诊断过期
async function setDiagnosticExpired(item) {
  const { orderId } = item;
  try {
    const res = await db
      .collection("diagnostic-record")
      .updateMany({ orderId, status: "INIT" }, { $set: { status: "EXPIRED" } });
    return { success: true, message: "操作成功" };
  } catch (e) {
    return { success: false, message: e.message || "操作失败" };
  }
}

async function getHisStoreRegList(ctx) {
  const { storeId } = ctx;
  if (typeof storeId !== "string" || storeId.trim() === "") {
    return { success: false, message: "药店id不能为空" };
  }
  try {
    const { success, list, message } = await zytHis({
      type: "getHisStoreRegList",
      storeId,
    });
    if (success && list.length) {
      const registerids = list.map((i) => i.registerid);
      const existRegisteridData = await db
        .collection("consult-order")
        .find(
          { registerId: { $in: registerids } },
          { projection: { _id: 0, registerId: 1 } }
        )
        .toArray();
      const existRegisterids = existRegisteridData.map((i) => i.registerId);

      const data = list.filter((i) => !existRegisterids.includes(i.registerid));

      return { success, list: data, message };
    }
    if (success) {
      return { success, list, message };
    }
    return { success, message };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function validHlwOrderConfig(ctx) {
  try {
    const { key, corpId } = ctx;
    if (typeof key !== "string" || key.trim() === "") {
      return { success: false, message: "获取失败" };
    }
    const res = await getConfig(corpId);
    const data = res[key];
    if (data) {
      return { success: true, message: "获取成功", data };
    }
    return { success: false, message: "获取失败" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
/**
 * 用户退费导致 取消订单
 * @param {*} item
 * @returns
 */
async function customerRefundOrder(item) {
  const { registerId, patientId, refundType = "OFFLINEREFUND" } = item;
  if (
    typeof registerId !== "string" ||
    registerId.trim() === "" ||
    typeof patientId !== "string" ||
    patientId.trim() === ""
  ) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  const msg = RefundOrderScene[refundType] || RefundOrderScene.OFFLINEREFUND;
  try {
    // 获取订单
    const record = await db.collection("consult-order").findOne(
      { registerId, patientId },
      {
        projection: {
          _id: 1,
          doctorCode: 1,
          orderId: 1,
          corpId: 1,
          orderStatus: 1,
        },
      }
    );
    if (!record) return { success: false, message: "订单不存在" };
    if (["finished"].includes(record.orderStatus)) {
      return {
        success: true,
        message: "订单已结束，无法取消",
        orderId: typeof record.orderId === "string" ? record.orderId : "",
      };
    }
    if (record.orderStatus === "cancelled") {
      return {
        success: true,
        message: "订单已取消",
        orderId: typeof record.orderId === "string" ? record.orderId : "",
      };
    }
    const res = await db.collection("consult-order").updateOne(
      { _id: record._id },
      {
        $set: {
          orderStatus: "cancelled",
          payStatus: "cancelled",
          payResult: "患者已退费",
          reason: "患者已退费",
          updateTime: Date.now(),
        },
      }
    );
    if (["processing", "pending", "completed"].includes(record.orderStatus)) {
      await tencentIM({
        type: "sendSystemNotification",
        formAccount: record.orderId,
        toAccount: record.doctorCode,
        SyncOtherMachine: 1,
        corpId: record.corpId,
        msgBody: [
          {
            MsgType: "TIMCustomElem",
            MsgContent: {
              Data: "CANCELLED",
              Desc: "notification",
              Ext: `本次问诊已取消，取消原因：${msg}。`,
            },
          },
        ],
      });
    }
    return {
      success: true,
      message: "取消成功",
      orderId: typeof record.orderId === "string" ? record.orderId : "",
    };
  } catch (error) {
    return {
      success: false,
      message: "取消失败",
    };
  }
}

async function getPatientOrderList(ctx) {
  const { patientId, startDate, endDate, isPres, orderStatus } = ctx;
  if (typeof patientId !== "string" || patientId.trim() === "") {
    return { success: false, message: "患者id不能为空" };
  }
  const startTime =
    startDate && dayjs(startDate).isValid()
      ? dayjs(startDate).startOf("day").valueOf()
      : "";
  const endTime =
    endDate && dayjs(endDate).isValid()
      ? dayjs(endDate).endOf("day").valueOf()
      : "";
  try {
    const query = { patientId, payStatus: "success" };
    if (typeof orderStatus === "string" && orderStatus.trim() !== "") {
      query.orderStatus = orderStatus.trim();
    }
    if (typeof isPres === "boolean") {
      query.preOrderId = { $exists: isPres };
    }
    if (startTime && endTime) {
      query.createTime = { $gte: startTime, $lte: endTime };
    }
    const list = await db
      .collection("consult-order")
      .find(query)
      .sort({ createTime: -1 })
      .toArray();
    return { success: true, message: "查询成功", list };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getOrderPatientList(ctx) {
  const { doctorCode, name, mobile } = ctx;
  const query = {};
  if (typeof doctorCode === "string" && doctorCode.trim()) {
    query.doctorCode = doctorCode.trim();
  }
  if (typeof name === "string" && name.trim()) {
    query.name = { $regex: name.trim(), $options: "i" };
  }
  if (typeof mobile === "string" && mobile.trim()) {
    query.mobile = { $regex: mobile.trim(), $options: "i" };
  }
  const page = parseInt(ctx.page) > 0 ? parseInt(ctx.page) : 1;
  const pageSize = parseInt(ctx.pageSize) > 0 ? parseInt(ctx.pageSize) : 10;
  try {
    const list = await db
      .collection("consult-order")
      .aggregate([
        { $match: query },
        {
          $group: {
            _id: "$patientId",
            blhno: { $first: "$blhno" },
            name: { $first: "$name" },
            sex: { $first: "$sex" },
            age: { $first: "$age" },
            mobile: { $first: "$mobile" },
            idCard: { $first: "$idCard" },
            address: { $first: "$address" },
            createTime: { $first: "$createTime" },
            patientId: { $first: "$patientId" },
          },
        },
        { $sort: { createTime: -1 } },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
      ])
      .toArray();
    const [stats] = await db
      .collection("consult-order")
      .aggregate([
        { $match: query },
        {
          $group: {
            _id: "$patientId",
          },
        },
        { $count: "total" },
      ])
      .toArray();
    const total = stats ? stats.total : 0;
    return { success: true, message: "查询成功", list, total };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getHlwOrderList(ctx) {
  try {
    const query = {};
    const page = parseInt(ctx.page) > 0 ? parseInt(ctx.page) : 1;
    const pageSize = parseInt(ctx.pageSize) > 0 ? parseInt(ctx.pageSize) : 10;
    if (typeof ctx.name === "string" && ctx.name.trim() != "") {
      query.name = { $regex: ".*" + ctx.name.trim() + ".*", $options: "i" };
    }
    if (typeof ctx.orderSource === "string" && ctx.orderSource.trim() != "") {
      query.orderSource = ctx.orderSource;
    }
    if (typeof ctx.orderStatus === "string" && ctx.orderStatus.trim() != "") {
      query.orderStatus = ctx.orderStatus;
    }
    if (Array.isArray(ctx.doctorCodes) && ctx.doctorCodes.length) {
      query.doctorCode = { $in: ctx.doctorCodes };
    }
    if (typeof ctx.isPres === "boolean") {
      query.preOrderId = { $exists: ctx.isPres };
    }
    if (Array.isArray(ctx.storeIds) && ctx.storeIds.length) {
      query.drugStoreId = { $in: ctx.storeIds };
    }
    const { startDate, endDate } = ctx;
    if (startDate && dayjs(startDate).isValid()) {
      query.createTime = { $gte: dayjs(startDate).startOf("day").valueOf() };
    }
    if (endDate && dayjs(endDate).isValid()) {
      query.createTime = {
        ...(query.createTime || {}),
        $lte: dayjs(endDate).endOf("day").valueOf(),
      };
    }
    let extraQuery = null
    if (ctx.isDoctorView === true) {
      extraQuery = {
        orderStatus: { $ne: 'pending' },
        payStatus: { $ne: 'pending' },
      }
    }
    const allQuery = extraQuery ? { $and: [extraQuery, query] } : query;


    const list = await db
      .collection("consult-order")
      .find(allQuery)
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const total = await db.collection("consult-order").countDocuments(allQuery);
    return { success: true, list, message: "查询成功", total };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function applyRefundOrder(ctx) {
  const { id: _id, orderId, reason } = ctx;
  if (
    typeof _id !== "string" ||
    _id.trim() === "" ||
    typeof orderId !== "string" ||
    orderId.trim() === ""
  ) {
    return { success: false, message: "参数错误" };
  }
  if (typeof reason !== "string" || reason.trim() === "") {
    return { success: false, message: "退款原因不能为空" };
  }
  if (reason.trim().length > 200) {
    return { success: false, message: "退款原因最多输入200字" };
  }
  try {
    const refundInfo = {
      status: "INIT",
      createTime: Date.now(),
      reason: reason.trim(),
    };
    const res = await db
      .collection("consult-order")
      .updateOne(
        { _id: new ObjectId(_id), orderId, refundInfo: { $exists: false } },
        { $set: { refundInfo } }
      );
    if (res.matchedCount === 0) {
      return { success: false, message: "订单不存在或者已经存在申请信息" };
    }
    if (res.modifiedCount === 1) {
      return { success: true, message: "申请成功", data: refundInfo };
    }
    return { success: false, message: "申请失败" };
  } catch (e) {
    return { success: false, message: e.message || "申请退款失败" };
  }
}

async function getRefundOrderList(ctx) {
  try {
    const query = {
      refundInfo: { $exists: true },
      doctorCode:
        typeof ctx.doctorCode === "string" ? ctx.doctorCode.trim() : "",
    };
    if (typeof ctx.name === "string" && ctx.name.trim() != "") {
      query.name = { $regex: ".*" + ctx.name.trim() + ".*", $options: "i" };
    }
    if (typeof ctx.mobile === "string" && ctx.mobile.trim() != "") {
      query.mobile = { $regex: ".*" + ctx.mobile.trim() + ".*", $options: "i" };
    }
    if (typeof ctx.status === "string" && ctx.status.trim() != "") {
      query["refundInfo.status"] = ctx.status;
    }
    const page = parseInt(ctx.page) > 0 ? parseInt(ctx.page) : 1;
    const pageSize = parseInt(ctx.pageSize) > 0 ? parseInt(ctx.pageSize) : 10;
    const projection = {
      _id: 1,
      name: 1,
      doctorName: 1,
      mobile: 1,
      refundInfo: 1,
      orderId: 1,
    }
    // const list = await db.collection('consult-order').find(query, { projection }).sort({ 'refundInfo.createTime': -1 }).skip((page - 1) * pageSize).limit(pageSize).toArray();
    const list = await db.collection('consult-order').aggregate([
      { $match: query },
      { $project: projection },
      {
        $addFields: {
          initFirst: { $cond: [{ $eq: ['$refundInfo.status', 'INIT'] }, 0, 1] }
        }
      },
      { $sort: { initFirst: 1, 'refundInfo.createTime': -1, _id: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize }
    ]).toArray();
    const total = await db.collection('consult-order').countDocuments(query);
    const count = await db.collection('consult-order').countDocuments({ ...query, 'refundInfo.status': 'INIT' });
    return { success: true, list, message: '查询成功', total, count }
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function handleRefundOrder(ctx) {
  const { id: _id, orderId, status, reason, operatorId, operator } = ctx;
  if (
    typeof _id !== "string" ||
    _id.trim() === "" ||
    typeof orderId !== "string" ||
    orderId.trim() === ""
  ) {
    return { success: false, message: "参数错误" };
  }
  if (typeof status !== "string" || !["PASS", "REJECT"].includes(status)) {
    return { success: false, message: "无效的退费状态" };
  }
  if (
    status === "REJECT" &&
    (typeof reason !== "string" || reason.trim() === "")
  ) {
    return { success: false, message: "拒绝退费原因不能为空" };
  }
  try {
    const doctorCode = typeof ctx.doctorCode === "string" ? ctx.doctorCode : "";
    const refundInfo = {
      "refundInfo.status": status,
      "refundInfo.rejectReason": status === "REJECT" ? reason.trim() : "",
      "refundInfo.updateTime": Date.now(),
      "refundInfo.operatorId":
        typeof operatorId === "string" ? operatorId.trim() : "",
      "refundInfo.operator":
        typeof operator === "string" ? operator.trim() : "",
    };
    const record = await db
      .collection("consult-order")
      .findOne(
        {
          _id: new ObjectId(_id),
          orderId,
          "refundInfo.status": "INIT",
          doctorCode,
        },
        { _id: 1 }
      );
    const rxApi = require("../diagnostic-record");
    if (!record) {
      return { success: false, message: "订单不存在或者退费申请已经处理" };
    }

    if (status === "PASS") {
      const order = await db
        .collection("consult-order")
        .findOne(
          { orderId },
          { projection: { registerId: 1, patientId: 1, medorgOrderNo: 1 } }
        );
      if (order && order.registerId && order.patientId) {
        const { success, message } = await zytHis({
          type: "hlwRefund",
          patientId: order.patientId,
          registerId: order.registerId,
          medorgOrderNo: order.medorgOrderNo,
        });
        if (!success) {
          return { success: false, message: message || "退费失败", errType: 'HIS_REFUND_FAIL' };
        }
        await customerRefundOrder({
          registerId: order.registerId,
          patientId: order.patientId,
          refundType: "DOCTORAGREEREFUND",
        });
        await rxApi({ type: "discardDiagnosticRecord", orderId }, db);
      }
    }
    await db
      .collection("consult-order")
      .updateOne({ _id: record._id }, { $set: refundInfo });
    return { success: true, message: "操作成功", data: refundInfo };
  } catch (e) {
    return { success: false, message: e.message || "操作失败" };
  }
}
