let db = "";
const { ObjectId } = require("mongodb");
const validator = require('../../utils/validator.js')
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getDiagnosisList":
      return await getDiagnosisList(item);
    case "deleteHlwDiagnosis":
      return await deleteHlwDiagnosis(item);
    case "updateHlwDiagnosis":
      return await updateHlwDiagnosis(item);
    case "addHlwDiagnosis":
      return await addHlwDiagnosis(item);
    case "updateHlwDiagnosisCategory":
      return await updateHlwDiagnosisCategory(item)
  }
};

// 获取诊断列表

async function getDiagnosisList(item) {
  const { page, pageSize, name, code, keyword, category } = item;
  try {
    // 调用mongo 数据库查询
    let query = {};
    if (name && typeof name === "string") {
      query.name = new RegExp(name.trim(), "i");
    }
    if (code && typeof code === "string") {
      query.code = new RegExp(code.trim(), "i");
    }
    if (category && typeof category === "string") {
      query.category = new RegExp(category.trim(), "i");
    }
    if (typeof keyword == "string" && keyword.trim()) {
      query.$or = [
        { name: new RegExp(keyword.trim(), "i") },
        { code: new RegExp(keyword.trim(), "i") },
        { category: new RegExp(keyword.trim(), "i") },
        { pinyin: new RegExp(keyword.trim(), "i") },
      ];
    }
    const res = await db
      .collection("hlw-diagnosis")
      .find(query)
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const total = await db.collection("hlw-diagnosis").countDocuments(query);
    return {
      success: true,
      message: "查询成功",
      data: res,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

// 删除诊断信息
async function deleteHlwDiagnosis(item) {
  const { id } = item;
  if (typeof id !== 'string' || id.trim() === '') {
    return { success: false, message: 'id不能为空' }
  }
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("hlw-diagnosis").deleteOne({
      _id: new ObjectId(id),
    });
    return {
      success: true,
      message: "删除成功",
      res
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "删除失败",
    };
  }
}

// 更新诊断信息
async function updateHlwDiagnosis(item) {
  if (typeof item.id !== 'string' || item.id.trim() === '') {
    return { success: false, message: 'id不能为空' }
  }
  try {
    // 调用mongo 数据库更新
    const [valid, data] = verifyDisease(item);
    if (!valid) return { success: false, message: data }
    data.updateTime = Date.now()
    const res = await db
      .collection("hlw-diagnosis")
      .updateOne(
        { _id: new ObjectId(item.id) },
        { $set: data }
      );
    if (res.matchedCount === 0) {
      return { success: false, message: '未查询到诊断' }
    }
    return {
      success: true,
      message: "更新成功",
      res
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "更新失败",
    };
  }
}

async function addHlwDiagnosis(item) {
  try {
    // 调用mongo 数据库新增
    const [valid, data] = verifyDisease(item);
    if (!valid) return { success: false, message: data }
    data.createTime = Date.now()
    const res = await db.collection("hlw-diagnosis").insertOne(data);
    return {
      success: true,
      message: "新增成功",
      id: res.insertedId
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "新增失败",
    };
  }
}

async function updateHlwDiagnosisCategory(ctx) {
  const oldCategory = typeof ctx.oldCategory === 'string' ? ctx.oldCategory.trim() : '';
  const newCategory = typeof ctx.newCategory === 'string' ? ctx.newCategory.trim() : undefined;
  if (!oldCategory || newCategory == undefined) {
    return { success: false, message: '参数有误' }
  }
  try {
    const res = await db.collection("hlw-diagnosis").updateMany(
      { category: oldCategory },
      { $set: { category: newCategory } }
    );
    return {
      success: true,
      message: "更新成功",
      modifiedCount: res.modifiedCount
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "更新失败",
    };
  }
}


function verifyDisease(ctx) {
  const [valid, name] = validator.verifyString(ctx.name, "诊断名称", 1);
  if (!valid) return [valid, name]
  const [valid1, code] = validator.verifyString(ctx.code, "诊断编码", 1);
  if (!valid1) return [valid1, code]
  const [valid2, pinyin] = validator.verifyString(ctx.pinyin, "诊断拼音码", 1);
  if (!valid2) return [valid2, pinyin]
  const [, category] = validator.verifyString(ctx.category, "常用诊断名");
  return [true, { name, code, pinyin, category }]
}

