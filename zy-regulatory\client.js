const axios = require('axios');
const config = require('./config');
const utils = require('./utils');

/**
 * 浙江省互联网医院监管平台API客户端
 */
class RegulatoryClient {
    constructor() {
        this.httpClient = axios.create({
            timeout: config.timeout,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    
    /**
     * 发送数据到监管平台
     * @param {string} method 服务方法名
     * @param {object|array} data 要发送的数据
     * @returns {Promise<object>} 响应结果
     */
    async post(method, data) {
        try {
            // 1. 序列化并加密请求体
            const jsonData = JSON.stringify(data);
            const encryptedBody = utils.aesEncrypt(jsonData, config.aesKey);
            console.log(`📄 原始数据: ${data.length}条 -> 加密: ${encryptedBody.length}字节`);

            // 2. 生成请求头和签名
            const headers = utils.generateHeaders(config.appId, method);
            const signParams = { ...headers, requestBody: encryptedBody };
            const signature = utils.generateSignature(signParams, config.appSecret);
            headers['X-Ca-Signature'] = signature;
            console.log(`🔐 签名: ${signature.substring(0, 16)}... 时间戳: ${headers['X-Ca-Timestamp']}`);

            // 3. 发送请求
            const response = await this.httpClient.post(
                config.apiUrl,
                encryptedBody,
                { headers }
            );
            
            // 4. 检查响应
            if (response.data.code !== 200) {
                throw new Error(`API错误: ${response.data.code} - ${response.data.msg}`);
            }

            return response.data;

        } catch (error) {
            console.error('❌ 监管平台API调用失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 带重试的请求
     * @param {string} method 服务方法名
     * @param {object|array} data 要发送的数据
     * @returns {Promise<object>} 响应结果
     */
    async postWithRetry(method, data) {
        return utils.retry(
            () => this.post(method, data),
            config.retryTimes,
            config.retryDelay
        );
    }
}

module.exports = new RegulatoryClient();
