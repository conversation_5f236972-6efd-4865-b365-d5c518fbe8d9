import { ObjectId } from "mongodb";

type HIS_PAY_STATUS = '0' | '1' | '5' | 'x'; // 0 已支付 1 已退费 5 未支付 x 已作废
type ORDER_SOURCE = 'ALIPAY_MINI' | 'MATEPAD'; // 订单来源 ALIPAY_MINI 支付宝小程序 MATEPAD 华为平板

interface Drug {
  _id: string;
  product_id?: number;
  product_id_str?: string; // product_id字符串形式
  specification: string;// 规格
  manufacturer: string;// 生产厂家
  unit: string;// 单位
  insurance_code: string;// 医保编码
  dosage: number;// 单次剂量
  dosage_unit: string;// 单次剂量单位
  quantity: number;// 数量
  usageName: string;// 用法
  usageCode: string;// 用法编码
  frequencyName: string;// 频次
  frequencyCode: string;// 频次编码
  memo: string;// 备注
}

interface Settlement {
  status: HIS_PAY_STATUS;// 结算状态
  total_fee: string;// 总费用
  yb_fee: string;// 医保费用
  chage_date: string;// 结算日期
  patientid: string;// 患者id
  registerid: string;// 挂号id
  medorg_order_no: string;// 医保订单号
}

interface Order {
  _id: ObjectId; // 数据库中的唯一标识符
  hospitalId: number;
  diseases: string[];// 线下确认的疾病 最多五种
  description: string;// 疾病描述 最多300字
  images: string[];// 疾病图片 最多九张
  drugs: Drug[];// 疾病相关药品
  patientId: string;// 患者id
  idCard: string;// 患者身份证号
  name: string;// 患者姓名
  mobile: string;// 患者电话
  blhno: string;// 病历号
  age: number;// 患者年龄
  sex: string;// 患者性别  
  address: string;// 患者地址
  pastHistory: string;// 患者既往史
  allergyHistory: string;// 患者过敏史
  familyHistory: string;// 患者家族史
  gestation: string;// 患者孕产史
  liver: string;// 患者肝功能
  renal: string;// 患者肾功能
  pastHistoryStr: string;// 患者既往史展示文字
  drugStoreId: string;// 药店id
  drugStoreNo: string;// 药店编码
  drugStoreName: string;// 药店名称
  doctorName: string;// 医生姓名
  doctorCode: string;// 医生编码
  doctorTitle: string;// 医生职称
  deptName: string;// 科室名称
  unitCode: string;// 科室编码
  orderId: string;// 订单号
  registerId: string;// his挂号id
  medorg_order_no: string;// 医保订单号
  createTime: number;// 创建时间
  orderStatus: string;// 订单状态
  payStatus: string;// 支付状态
  corpId: string;// 机构id
  payExpireTime: number;// 支付过期时间
  chargeCode: string;// 费用编码
  chargeFee: number;// 费用金额
  settlement?: Settlement;// his结算状态
  updateTime: number;// 更新时间
  expireTime: number;// 过期时间
  orderSource: ORDER_SOURCE;// 订单来源
  signature: string;// 签名图片路径
}