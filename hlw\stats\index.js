const dayjs = require("dayjs");
const setCountText = require('./order-status');
const { ObjectId } = require("mongodb");

module.exports = async (item, db) => {
  switch (item.statsType) {
    case 'order':
      return await getOrderCount(item, db);
    case 'doctorOrder':
      return await getDoctorOrderCount(item, db);
    case 'getStoreMatePadRxStats':
      return await getStoreMatePadRxStats(item, db);
    // case 'storeOrder':
    //   return await getStoreCount(item, db);
    // case 'getPeriodRxCount':
    //   return await getPeriodRxCount(item, db);
    case "zyStoreRx":
      return await getZyStoreRxCount(item, db);
    // case "oldRx":
    //   return await getZyStoreAgeRxCount(item, db);
    default:
      return { success: false, message: '未知统计类型！！' }
  }
};

function getDateRange({ dates }) {
  const dateArr = typeof dates === 'string' ? dates.split(',').filter(i => i && dayjs(i).isValid()).map(i => dayjs(i)) : [];
  const [dateStart = dayjs(), dateEnd = dayjs(dateStart)] = dateArr;
  const minDate = dateStart.isBefore(dateEnd) ? dateStart : dateEnd;
  const maxDate = dateStart.isBefore(dateEnd) ? dateEnd : dateStart;
  if (Math.abs(minDate.diff(maxDate, 'day')) > 30) {
    return [minDate.startOf('day').valueOf(), minDate.add(31, 'day').endOf('day').valueOf()]
  }
  return [minDate.startOf('day').valueOf(), maxDate.endOf('day').valueOf()]
}

async function getOrderCount(params, db) {
  try {
    const [startTime, endTime] = getDateRange(params);
    const data = await db.collection('consult-order').aggregate([
      { $match: { createTime: { $gte: startTime, $lte: endTime } } },
      {
        $group: {
          _id: {
            orderStatus: "$orderStatus",
            payStatus: "$payStatus"
          },
          count: { $sum: 1 }  // 统计每个组合的数量
        }
      },
      {
        $project: {
          _id: 0,  // 如果不需要显示 _id
          orderStatus: "$_id.orderStatus",
          payStatus: "$_id.payStatus",
          count: 1
        }
      }
    ]).toArray();
    const res = setCountText(data);
    const allCount = res.reduce((acc, cur) => acc + cur.count, 0);
    return { success: true, message: '查询成功', startDate: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'), endDate: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'), allCount: allCount, data: res }
  } catch (err) {
    return { success: false, message: err.message }
  }
}

async function getDoctorOrderCount(params, db) {
  try {
    const [startTime, endTime] = getDateRange(params);
    const doctorCount = await db.collection('hlw-doctor').countDocuments({ job: 'doctor' });
    const doctorList = await db.collection('hlw-doctor').find({ job: 'doctor' }, { projection: { doctorName: 1, doctorNo: 1, _id: 0 } }).toArray()
    const data = await db.collection('consult-order').aggregate([
      { $match: { createTime: { $gte: startTime, $lte: endTime } } },
      {
        $group: {
          _id: {
            orderStatus: "$orderStatus",
            payStatus: "$payStatus",
            doctorCode: '$doctorCode'
          },
          count: { $sum: 1 }  // 统计每个组合的数量
        }
      },
      {
        $project: {
          _id: 0,  // 如果不需要显示 _id
          doctorCode: "$_id.doctorCode",
          orderStatus: "$_id.orderStatus",
          payStatus: "$_id.payStatus",
          count: 1
        }
      }
    ]).toArray();
    const res = setCountText(data, doctorList);
    const allCount = res.reduce((acc, cur) => acc + (cur.allCount || 0), 0);
    return { success: true, message: '查询成功', startDate: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'), endDate: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'), allCount, doctorCount, statsCount: res.length, data: res }
  } catch (err) {
    return { success: false, message: err.message }
  }
}


async function getStoreCount(params, db) {
  try {
    let storeIds = [];
    const [startTime, endTime] = getDateRange(params);
    if (params.dataType === 'storeOrder' || params.dataType === 'storeRx') {
      if (typeof params.area !== 'string' || params.area.trim() === '') {
        return { success: false, message: '区域不能为空' }
      }
      const stores = await db.collection('store-list').find({ 'region.2': params.area }, { projection: { store_id: 1, _id: 0 } }).toArray();
      storeIds = stores.map(i => i.store_id);
    }
    // 统计越城区药店的今日订单数量 start 
    if (params.dataType === 'storeOrder') {
      const storeOrderStats = await db.collection('consult-order').aggregate([
        { $match: { drugStoreNo: { $in: storeIds }, createTime: { $gte: startTime, $lte: endTime } } },
        {
          $group: {
            _id: "$drugStoreNo",
            drugStoreName: { $first: "$drugStoreName" },
            count: { $sum: 1 }  // 统计每个组合的数量
          }
        }
      ]).toArray();
      const storeOrderRes = Array.from(storeOrderStats.values());
      return storeOrderRes
    }
    if (params.dataType === 'storeRx') {
      // 统计药店的处方数量 start
      const orders = await db.collection('consult-order').find({ drugStoreNo: { $in: storeIds }, createTime: { $gte: startTime, $lte: endTime } }, { projection: { orderId: 1, _id: 0, drugStoreName: 1, drugStoreNo: 1 } }).toArray();
      const orderIds = orders.map(i => i.orderId);
      const record = await db.collection('diagnostic-record').aggregate([
        { $match: { orderId: { $in: orderIds }, status: 'PASS' } },
        {
          $group: {
            _id: "$orderId",
            count: { $sum: 1 }  // 统计每个组合的数量
          }
        }
      ]).toArray();
      const om = record.reduce((acc, cur) => {
        acc[cur._id] = cur.count;
        return acc;
      }, {})
      const rxOrders = orders.filter(i => om[i.orderId] > 0);
      const storeRx = rxOrders.reduce((acc, cur) => {
        const data = acc.get(cur.drugStoreNo) || { drugStoreNo: cur.drugStoreNo, drugStoreName: cur.drugStoreName, count: 0 };
        data.count += 1;
        acc.set(cur.drugStoreNo, data);
        return acc;
      }, new Map())
      const storeRxRes = Array.from(storeRx.values());
      // 统计不同类型药店的数据
      const typeStore = storeRxRes.reduce((acc, cur) => {
        if (cur.drugStoreName.startsWith('浙江震元')) {
          acc.zy += cur.count;
        } else {
          acc.nzy += cur.count;
        }
        return acc
      }, { zy: 0, nzy: 0 })
      return { ...typeStore, storeRxRes }
    }
    if (params.dataType === 'doctorRx') {
      // 统计医生的处方数量 start
      const doctorRx = await db.collection('diagnostic-record').aggregate([
        { $match: { createTime: { $gte: startTime, $lte: endTime }, status: 'PASS', _id: { $nin: [new ObjectId('67eca29fecce43c72c51d1e7'), new ObjectId('67ecfe26ecce43c72c520ec7')] } } },
        {
          $group: {
            _id: "$doctorCode",
            doctorName: { $first: "$doctorName" },
            count: { $sum: 1 }  // 统计每个组合的数量
          }
        }
      ]).toArray();
      return doctorRx
    }

  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function getPeriodRxCount(params, db) {
  const data = params.date && dayjs(params.period).isValid() ? dayjs(params.date).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD');
  try {
    let time = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const group = [];
    while (dayjs(time).isBefore(dayjs().endOf('day'))) {
      group.push([dayjs(time).valueOf(), dayjs(time).add(30, 'minutes').valueOf()])
      // group.push({ $match: { createTime: { $gte: dayjs(time).format('YYYY-MM-DD HH:mm:ss'), $lt: dayjs(time).add(30, 'minutes').format('YYYY-MM-DD HH:mm:ss') }, status: 'PASS' } })
      time = dayjs(time).add(30, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    }
    const result = [];
    for (let [start, end] of group) {
      const total = await db.collection('diagnostic-record').countDocuments({ createTime: { $gte: start, $lt: end }, status: 'PASS' });
      result.push({ start: dayjs(start).format('YYYY-MM-DD HH:mm:ss'), end: dayjs(end).format('YYYY-MM-DD HH:mm:ss'), total })
    }
    return result
  } catch (e) {
    return { success: false, message: e.message }
  }
}


async function getStoreMatePadRxStats(params, db) {
  const [startTime, endTime] = getDateRange(params);
  try {
    const res = await db.collection('diagnostic-record').aggregate([
      { $match: { orderSource: 'MATEPAD', createTime: { $gte: startTime, $lte: endTime }, status: 'PASS' } },
      {
        $group: {
          _id: '$drugStoreNo',
          count: { $sum: 1 }
        }
      }
    ]).toArray();
    const stores = await db.collection('store-list').find({ store_id: { $in: res.map(i => i._id) } }, { projection: { store_id: 1, name: 1, _id: 0 } }).toArray();
    return { success: true, data: res, stores }
  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function getZyStoreRxCount(ctx, db) {
  const [startTime, endTime, storeType, source] = getDateRange(ctx);
  const storeTypes = storeType === 'other' ? ['其他'] : ['加盟', '直营']
  try {
    const stores = await db.collection('store-list').find({ type: { $in: storeTypes } }, { projection: { store_id: 1, name: 1, _id: 0 } }).toArray();
    const storeIds = stores.map(i => i.store_id);
    const m = stores.reduce((acc, cur) => {
      // console.log(cur.store_id, cur.name)
      acc.set(cur.store_id, cur.name);
      return acc;
    }, new Map())
    const matches = { drugStoreNo: { $in: storeIds }, createTime: { $gte: startTime, $lte: endTime }, status: 'PASS' };
    if (source) {
      matches.orderSource = source
    }
    const res = await db.collection('diagnostic-record').aggregate([
      { $match: matches },
      {
        $addFields: {
          date: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: { $toDate: "$createTime" },
              timezone: "Asia/Shanghai" // 设置为中国标准时间
            }
          }
        }
      },
      {
        $group: {
          _id: { date: "$date", id: "$drugStoreNo" },
          date: { $first: "$date" },
          drugStoreNo: { $first: "$drugStoreNo" },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: 1,
          drugStoreNo: 1,
          count: 1
        }
      }
    ])
      .toArray();
    res.forEach(i => {
      i.name = m.get(i.drugStoreNo) || ''
    })
    return res
  } catch (e) {
    return { success: false, message: e.message }
  }
}

async function getZyStoreAgeRxCount(ctx, db) {
  const [startTime, endTime] = getDateRange(ctx);
  try {
    const query = { createTime: { $gte: startTime, $lte: endTime } }
    if (ctx.ageType === 'old') {
      query.age = { $gte: 70 }
    } else {
      query.age = { $lt: 70 }
    }
    const orders = await db.collection('consult-order').find(query, { projection: { orderId: 1, _id: 0 } }).toArray();
    const orderIds = orders.map(i => i.orderId);
    const rxCountGroup = await db.collection('diagnostic-record').aggregate([
      { $match: { createTime: { $gte: startTime, $lte: endTime }, status: 'PASS', orderId: { $in: orderIds } } },
      {
        $group: {
          _id: '$drugStoreNo',
          drugStoreNo: { $first: "$drugStoreNo" },
          count: { $sum: 1 }
        }
      },
    ]).toArray();
    const stores = await db.collection('store-list').find({ store_id: { $in: rxCountGroup.map(i => i.drugStoreNo) } }, { projection: { store_id: 1, name: 1, _id: 0, type: 1, region: 1 } }).toArray();
    const m = stores.reduce((acc, cur) => {
      acc.set(cur.store_id, cur);
      return acc;
    }, new Map())

    rxCountGroup.forEach(i => {
      const store = m.get(i.drugStoreNo) || null;
      if (store) {
        i.name = store.name || ''
        i.type = store.type || ''
        i.area = Array.isArray(store.region) ? store.region[2] : ''
      } else {
        i.name = ''
        i.type = ''
        i.area = ''
      }
    })
    return rxCountGroup
  } catch (e) {
    return { success: false, message: e.message }
  }
}